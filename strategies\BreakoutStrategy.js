import { BaseStrategy } from './BaseStrategy.js';

export class BreakoutStrategy extends BaseStrategy {
  constructor(config = {}) {
    super({
      breakoutLookback: 20,
      confirmRetest: false, // optionally wait for a small pullback and rebound
      timeframe: '5m', // Default to 5-minute candles
      ...config
    });
  }

  shouldEnterTrade(currentPrice, marketData) {
    // Don't enter if we already have a position
    if (this.hasPosition(marketData)) {
      const position = marketData.currentPosition;
      return {
        shouldEnter: false,
        reason: `Already holding ${position.quantity.toFixed(8)} BTC at avg price $${position.averagePrice.toFixed(2)}`
      };
    }

    const { breakoutLookback, confirmRetest } = this.config;

    // Check if we have enough candle data
    if (!this.hasEnoughCandles(breakoutLookback)) {
      return {
        shouldEnter: false,
        reason: `Accumulating ${this.getTimeframeDescription()} candle data (${this.candleAggregator.completedCandles.length}/${breakoutLookback} required)`
      };
    }

    // Get the highest high over the lookback period (excluding current candle)
    const previousHigh = this.getHighestHigh(breakoutLookback);
    const closingPrices = this.getClosingPrices();
    const lastClose = closingPrices[closingPrices.length - 1];

    const breakoutOccurred = lastClose <= previousHigh && currentPrice > previousHigh;
    const breakoutPercent = ((currentPrice - previousHigh) / previousHigh * 100);

    if (breakoutOccurred && !confirmRetest) {
      return {
        shouldEnter: true,
        reason: `Breakout: price $${currentPrice.toFixed(2)} broke above ${breakoutLookback}-${this.getTimeframeDescription()} high $${previousHigh.toFixed(2)} (+${breakoutPercent.toFixed(1)}%)`
      };
    }

    if (breakoutOccurred && confirmRetest) {
      const recentLows = this.candleAggregator.getLowPrices(3);
      const prevLow = Math.min(...recentLows);
      const rebound = currentPrice > prevLow;
      return {
        shouldEnter: rebound,
        reason: rebound
          ? `Breakout confirmed: price $${currentPrice.toFixed(2)} broke ${this.getTimeframeDescription()} high $${previousHigh.toFixed(2)} and retested successfully`
          : `Breakout occurred but waiting for retest confirmation above $${prevLow.toFixed(2)}`
      };
    }

    // No breakout yet
    const distanceToBreakout = ((previousHigh - currentPrice) / currentPrice * 100);
    if (currentPrice < previousHigh) {
      return {
        shouldEnter: false,
        reason: `Waiting for breakout above ${breakoutLookback}-${this.getTimeframeDescription()} high $${previousHigh.toFixed(2)} (${distanceToBreakout.toFixed(1)}% to go)`
      };
    } else {
      return {
        shouldEnter: false,
        reason: `Price above recent high but no fresh breakout signal detected`
      };
    }
  }

  calculateStopLoss(currentPrice) {
    const stopPercent = this.calculateATR(this.priceHistory);
    return {
      stopPercent,
      stopPrice: currentPrice * (1 - stopPercent),
      reason: `ATR-based stop at ${Math.round(stopPercent * 10000) / 100}%`
    };
  }

  getPositionSize(availableCash, currentPrice) {
    return super.getPositionSize(availableCash, currentPrice);
  }

  getDescription() {
    return 'Enters when price breaks above recent highs, optionally with confirmation. Good for catching trend initiations.';
  }

  getConfigSchema() {
    return {
      ...super.getConfigSchema(),
      breakoutLookback: {
        type: 'number',
        label: 'Breakout Lookback',
        min: 5,
        max: 50,
        default: 20,
        description: 'Number of candles to look back for breakout detection'
      },
      confirmRetest: {
        type: 'boolean',
        label: 'Confirm with Retest',
        default: false,
        description: 'Wait for a small pullback and rebound after breakout before entering'
      },
      timeframe: {
        type: 'select',
        label: 'Timeframe',
        options: [
          { value: '1m', label: '1 Minute' },
          { value: '5m', label: '5 Minutes' },
          { value: '15m', label: '15 Minutes' },
          { value: '30m', label: '30 Minutes' },
          { value: '1h', label: '1 Hour' },
          { value: '4h', label: '4 Hours' },
          { value: '1d', label: '1 Day' }
        ],
        default: '5m',
        description: 'Time period for each candle in technical analysis'
      }
    };
  }
}
