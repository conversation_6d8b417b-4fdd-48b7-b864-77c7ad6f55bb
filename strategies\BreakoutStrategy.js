import { BaseStrategy } from './BaseStrategy.js';

export class BreakoutStrategy extends BaseStrategy {
  constructor(config = {}) {
    super({
      breakoutLookback: 20,
      confirmRetest: false, // optionally wait for a small pullback and rebound
      ...config
    });
  }

  shouldEnterTrade(currentPrice, marketData) {
    // Don't enter if we already have a position
    if (this.hasPosition(marketData)) {
      const position = marketData.currentPosition;
      return {
        shouldEnter: false,
        reason: `Already holding ${position.quantity.toFixed(8)} BTC at avg price $${position.averagePrice.toFixed(2)}`
      };
    }

    const { breakoutLookback, confirmRetest } = this.config;
    const requiredHistory = breakoutLookback + 1;

    if (this.priceHistory.length < requiredHistory) {
      return {
        shouldEnter: false,
        reason: `Accumulating price data (${this.priceHistory.length}/${requiredHistory} required)`
      };
    }

    const recentHighs = this.priceHistory.slice(-breakoutLookback - 1, -1);
    const previousHigh = Math.max(...recentHighs);
    const lastClose = this.priceHistory[this.priceHistory.length - 2];
    const breakoutOccurred = lastClose <= previousHigh && currentPrice > previousHigh;
    const breakoutPercent = ((currentPrice - previousHigh) / previousHigh * 100);

    if (breakoutOccurred && !confirmRetest) {
      return {
        shouldEnter: true,
        reason: `Breakout: price $${currentPrice.toFixed(2)} broke above ${breakoutLookback}-period high $${previousHigh.toFixed(2)} (+${breakoutPercent.toFixed(1)}%)`
      };
    }

    if (breakoutOccurred && confirmRetest) {
      const prevLow = Math.min(...this.priceHistory.slice(-3, -1));
      const rebound = currentPrice > prevLow;
      return {
        shouldEnter: rebound,
        reason: rebound
          ? `Breakout confirmed: price $${currentPrice.toFixed(2)} broke $${previousHigh.toFixed(2)} and retested successfully`
          : `Breakout occurred but waiting for retest confirmation above $${prevLow.toFixed(2)}`
      };
    }

    // No breakout yet
    const distanceToBreakout = ((previousHigh - currentPrice) / currentPrice * 100);
    if (currentPrice < previousHigh) {
      return {
        shouldEnter: false,
        reason: `Waiting for breakout above $${previousHigh.toFixed(2)} (${distanceToBreakout.toFixed(1)}% to go)`
      };
    } else {
      return {
        shouldEnter: false,
        reason: `Price above recent high but no fresh breakout signal detected`
      };
    }
  }

  calculateStopLoss(currentPrice) {
    const stopPercent = this.calculateATR(this.priceHistory);
    return {
      stopPercent,
      stopPrice: currentPrice * (1 - stopPercent),
      reason: `ATR-based stop at ${Math.round(stopPercent * 10000) / 100}%`
    };
  }

  getPositionSize(availableCash, currentPrice) {
    return super.getPositionSize(availableCash, currentPrice);
  }

  getDescription() {
    return 'Enters when price breaks above recent highs, optionally with confirmation. Good for catching trend initiations.';
  }

  getConfigSchema() {
    return {
      ...super.getConfigSchema(),
      breakoutLookback: {
        type: 'number',
        label: 'Breakout Lookback',
        min: 5,
        max: 50,
        default: 20,
        description: 'Lookback period for identifying breakout level'
      },
      confirmRetest: {
        type: 'boolean',
        label: 'Confirm with Retest',
        default: false,
        description: 'Wait for a small pullback and rebound after breakout before entering'
      }
    };
  }
}
