import { BaseStrategy } from './BaseStrategy.js';

export class BreakoutStrategy extends BaseStrategy {
  constructor(config = {}) {
    super({
      breakoutLookback: 20,
      confirmRetest: false, // optionally wait for a small pullback and rebound
      ...config
    });
  }

  shouldEnterTrade(currentPrice) {
    const { breakoutLookback, confirmRetest } = this.config;

    if (this.priceHistory.length < breakoutLookback + 1) {
      return { shouldEnter: false };
    }

    const recentHighs = this.priceHistory.slice(-breakoutLookback - 1, -1);
    const previousHigh = Math.max(...recentHighs);
    const lastClose = this.priceHistory[this.priceHistory.length - 2];
    const breakoutOccurred = lastClose <= previousHigh && currentPrice > previousHigh;

    if (breakoutOccurred && !confirmRetest) {
      return {
        shouldEnter: true,
        reason: `Price broke above recent high of ${previousHigh.toFixed(2)}`
      };
    }

    if (breakoutOccurred && confirmRetest) {
      const prevLow = Math.min(...this.priceHistory.slice(-3, -1));
      const rebound = currentPrice > prevLow;
      return {
        shouldEnter: rebound,
        reason: rebound
          ? 'Breakout with confirmation retest'
          : 'Waiting for retest confirmation'
      };
    }

    return { shouldEnter: false, reason: 'No breakout detected' };
  }

  calculateStopLoss(currentPrice) {
    const stopPercent = this.calculateATR(this.priceHistory);
    return {
      stopPercent,
      stopPrice: currentPrice * (1 - stopPercent),
      reason: `ATR-based stop at ${Math.round(stopPercent * 10000) / 100}%`
    };
  }

  getPositionSize(availableCash, currentPrice) {
    return super.getPositionSize(availableCash, currentPrice);
  }

  getDescription() {
    return 'Enters when price breaks above recent highs, optionally with confirmation. Good for catching trend initiations.';
  }

  getConfigSchema() {
    return {
      ...super.getConfigSchema(),
      breakoutLookback: {
        type: 'number',
        label: 'Breakout Lookback',
        min: 5,
        max: 50,
        default: 20,
        description: 'Lookback period for identifying breakout level'
      },
      confirmRetest: {
        type: 'boolean',
        label: 'Confirm with Retest',
        default: false,
        description: 'Wait for a small pullback and rebound after breakout before entering'
      }
    };
  }
}
