import { BaseStrategy } from './BaseStrategy.js';

export class MovingAverageCrossoverStrategy extends BaseStrategy {
  constructor(config = {}) {
    super({
      shortPeriod: 5,
      longPeriod: 20,
      ...config
    });
  }

  shouldEnterTrade(currentPrice, marketData) {
    // Don't enter if we already have a position
    if (this.hasPosition(marketData)) {
      const position = marketData.currentPosition;
      return {
        shouldEnter: false,
        reason: `Already holding ${position.quantity.toFixed(8)} BTC at avg price $${position.averagePrice.toFixed(2)}`
      };
    }

    const { shortPeriod, longPeriod } = this.config;
    const requiredHistory = Math.max(shortPeriod, longPeriod) + 1; // +1 for previous values

    // Check if we have enough price history
    if (this.priceHistory.length < requiredHistory) {
      return {
        shouldEnter: false,
        reason: `Accumulating price data (${this.priceHistory.length}/${requiredHistory} required)`
      };
    }

    const shortSMA = this.calculateSMA(this.priceHistory, shortPeriod);
    const longSMA = this.calculateSMA(this.priceHistory, longPeriod);

    if (!shortSMA || !longSMA) {
      return {
        shouldEnter: false,
        reason: 'Insufficient data for moving average calculation'
      };
    }

    const previousShortSMA = this.calculateSMA(this.priceHistory.slice(0, -1), shortPeriod);
    const previousLongSMA = this.calculateSMA(this.priceHistory.slice(0, -1), longPeriod);

    const crossover = previousShortSMA <= previousLongSMA && shortSMA > longSMA;
    const crossoverDistance = ((shortSMA - longSMA) / longSMA * 100);

    if (crossover) {
      return {
        shouldEnter: true,
        reason: `Golden cross: ${shortPeriod}-MA $${shortSMA.toFixed(2)} crossed above ${longPeriod}-MA $${longSMA.toFixed(2)} (${crossoverDistance.toFixed(1)}% above)`
      };
    } else if (shortSMA > longSMA) {
      return {
        shouldEnter: false,
        reason: `${shortPeriod}-MA above ${longPeriod}-MA but no fresh crossover (${crossoverDistance.toFixed(1)}% above)`
      };
    } else {
      const belowDistance = Math.abs(crossoverDistance);
      return {
        shouldEnter: false,
        reason: `Waiting for golden cross: ${shortPeriod}-MA $${shortSMA.toFixed(2)} below ${longPeriod}-MA $${longSMA.toFixed(2)} (${belowDistance.toFixed(1)}% below)`
      };
    }
  }

  calculateStopLoss(currentPrice) {
    const stopPercent = this.calculateATR(this.priceHistory);
    return {
      stopPercent,
      stopPrice: currentPrice * (1 - stopPercent),
      reason: `ATR-based stop at ${Math.round(stopPercent * 10000) / 100}%`
    };
  }

  getPositionSize(availableCash, currentPrice) {
    return super.getPositionSize(availableCash, currentPrice);
  }

  getDescription() {
    return 'Enters a trade when short-term SMA crosses above long-term SMA. Good for trend-following.';
  }

  getConfigSchema() {
    return {
      ...super.getConfigSchema(),
      shortPeriod: {
        type: 'number',
        label: 'Short SMA Period',
        default: 5,
        min: 2,
        max: 20
      },
      longPeriod: {
        type: 'number',
        label: 'Long SMA Period',
        default: 20,
        min: 5,
        max: 100
      }
    };
  }
}
