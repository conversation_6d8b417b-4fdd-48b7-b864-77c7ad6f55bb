import { BaseStrategy } from './BaseStrategy.js';

export class MovingAverageCrossoverStrategy extends BaseStrategy {
  constructor(config = {}) {
    super({
      shortPeriod: 5,
      longPeriod: 20,
      ...config
    });
  }

  shouldEnterTrade(currentPrice, marketData) {
    const { shortPeriod, longPeriod } = this.config;
    const shortSMA = this.calculateSMA(this.priceHistory, shortPeriod);
    const longSMA = this.calculateSMA(this.priceHistory, longPeriod);

    if (!shortSMA || !longSMA) return { shouldEnter: false };

    const previousShortSMA = this.calculateSMA(this.priceHistory.slice(0, -1), shortPeriod);
    const previousLongSMA = this.calculateSMA(this.priceHistory.slice(0, -1), longPeriod);

    const crossover = previousShortSMA <= previousLongSMA && shortSMA > longSMA;
    return {
      shouldEnter: crossover,
      reason: crossover ? 'Bullish SMA crossover' : 'No crossover'
    };
  }

  calculateStopLoss(currentPrice) {
    const stopPercent = this.calculateATR(this.priceHistory);
    return {
      stopPercent,
      stopPrice: currentPrice * (1 - stopPercent),
      reason: `ATR-based stop at ${Math.round(stopPercent * 10000) / 100}%`
    };
  }

  getPositionSize(availableCash, currentPrice) {
    return super.getPositionSize(availableCash, currentPrice);
  }

  getDescription() {
    return 'Enters a trade when short-term SMA crosses above long-term SMA. Good for trend-following.';
  }

  getConfigSchema() {
    return {
      ...super.getConfigSchema(),
      shortPeriod: {
        type: 'number',
        label: 'Short SMA Period',
        default: 5,
        min: 2,
        max: 20
      },
      longPeriod: {
        type: 'number',
        label: 'Long SMA Period',
        default: 20,
        min: 5,
        max: 100
      }
    };
  }
}
