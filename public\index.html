<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Robinhood Crypto Bot Dashboard</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Robinhood Crypto Bot</h1>
      <p>Automated Trading Dashboard</p>
    </div>
    
    <div class="stats-grid">
      <div class="stat-card">
        <h3>Bot Status</h3>
        <div class="stat-value" id="status">--</div>
      </div>
      
      <div class="stat-card">
        <h3>Total Trades</h3>
        <div class="stat-value" id="trades">0</div>
      </div>
      
      <div class="stat-card">
        <h3>Account Balance</h3>
        <div class="stat-value" id="balance">$0.00</div>
      </div>
      
      <div class="stat-card">
        <h3>BTC Holdings</h3>
        <div class="stat-value" id="btcHoldings">0.********</div>
      </div>
      
      <div class="stat-card">
        <h3>Current BTC Price</h3>
        <div class="stat-value" id="btcPrice">$0.00</div>
      </div>
      
      <div class="stat-card">
        <h3>Portfolio Value</h3>
        <div class="stat-value" id="portfolioValue">$0.00</div>
      </div>
      
      <div class="stat-card">
        <h3>P&L Today</h3>
        <div class="stat-value" id="pnl">$0.00</div>
      </div>
      
      <div class="stat-card">
        <h3>Stop Loss</h3>
        <div class="stat-value" id="stopLoss">None</div>
      </div>
    </div>

    <!-- Portfolio Balance Chart -->
    <div class="chart-section">
      <div class="chart-card">
        <div class="chart-header">
          <h3>Portfolio Balance</h3>
          <div class="balance-summary">
            <span class="balance-item">Cash: <span id="cashBalance">$0.00</span></span>
            <span class="balance-item">Holdings: <span id="holdingsValue">$0.00</span></span>
            <span class="balance-item">Total: <span id="totalValue">$0.00</span></span>
            <span class="balance-item pnl" id="pnlDisplay">P&L: $0.00</span>
          </div>
        </div>
        <div class="chart-container">
          <canvas id="portfolioChart"></canvas>
        </div>
      </div>
    </div>

    <!-- BTC Price Chart -->
    <div class="chart-section">
      <div class="chart-card">
        <div class="chart-header">
          <h3>BTC Price Chart (OHLC)</h3>
          <div class="chart-controls">
            <div class="timeframe-selector">
              <label>Timeframe:</label>
              <button class="timeframe-btn active" data-timeframe="1m">1m</button>
              <button class="timeframe-btn" data-timeframe="5m">5m</button>
              <button class="timeframe-btn" data-timeframe="15m">15m</button>
              <button class="timeframe-btn" data-timeframe="1h">1h</button>
              <button class="timeframe-btn" data-timeframe="4h">4h</button>
            </div>
            <div class="chart-period-selector">
              <label>Period:</label>
              <button class="period-btn active" data-period="1h">1H</button>
              <button class="period-btn" data-period="6h">6H</button>
              <button class="period-btn" data-period="1d">1D</button>
              <button class="period-btn" data-period="3d">3D</button>
              <button class="period-btn" data-period="1w">1W</button>
            </div>
          </div>
        </div>
        <div class="chart-container">
          <canvas id="priceChart"></canvas>
        </div>
      </div>
    </div>
    
    <div class="controls">
      <button class="toggle-btn" id="toggleBtn">Start Bot</button>
      
      <div class="balance-control">
        <label for="maxBalanceSlider">Max Trading Balance: $<span id="maxBalanceValue">0</span></label>
        <input type="range" id="maxBalanceSlider" min="0" max="1000" value="500" step="10" class="balance-slider">
        <div class="balance-info">
          <span class="balance-min">$0</span>
          <span class="balance-max">Available: $<span id="availableBalance">0</span></span>
        </div>
      </div>
    </div>

    <div class="strategy-section">
      <div class="strategy-card">
        <h3>Portfolio Strategy Management</h3>
        <div class="portfolio-overview">
          <div class="portfolio-stats">
            <div class="stat-item">
              <label>Active Strategies:</label>
              <span id="activeStrategies">--</span>
            </div>
            <div class="stat-item">
              <label>Last Rebalance:</label>
              <span id="lastRebalance">--</span>
            </div>
            <div class="stat-item">
              <label>Rebalance Frequency:</label>
              <span id="rebalanceFrequency">--</span>
            </div>
          </div>
        </div>
        
        <div class="strategies-grid" id="strategiesGrid">
          <!-- Individual strategy cards will be populated here -->
        </div>
      </div>
    </div>

    <div class="logs-section">
      <div class="logs-card">
        <h3>Trading Activity</h3>
        <div id="log" class="log-container"></div>
      </div>
    </div>
  </div>

  <!-- Strategy Configuration Modal -->
  <div id="strategyConfigModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3 id="configModalTitle">Configure Strategy</h3>
        <button class="modal-close" onclick="closeStrategyConfig()">&times;</button>
      </div>
      <div class="modal-body">
        <div id="configModalDescription" class="config-description"></div>
        <form id="strategyConfigForm">
          <div id="configFields" class="config-fields">
            <!-- Dynamic config fields will be inserted here -->
          </div>
          <div class="config-actions">
            <button type="button" class="btn btn-secondary" onclick="closeStrategyConfig()">Cancel</button>
            <button type="button" class="btn btn-primary" onclick="resetStrategyConfig()">Reset to Defaults</button>
            <button type="submit" class="btn btn-success">Save Configuration</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chartjs-chart-financial/dist/chartjs-chart-financial.min.js"></script>
  <script src="script.js?v=19"></script>
</body>
</html>
