// Centralized candle aggregation service
// Provides OHLC candle data for multiple timeframes to all strategies

import { CandleAggregator } from './CandleAggregator.js';
import fs from 'fs';
import path from 'path';

export class CandleAggregatorService {
  constructor() {
    // Map of timeframe -> CandleAggregator
    this.aggregators = new Map();

    // Standard timeframes
    this.supportedTimeframes = ['1m', '5m', '15m', '30m', '1h', '4h', '1d'];

    // Data persistence settings
    this.dataDir = './data/candles';
    this.retentionDays = 7; // Keep 1 week of data
    this.saveIntervalMs = 5 * 60 * 1000; // Save every 5 minutes
    this.lastSaveTime = 0;

    // Ensure data directory exists
    this.ensureDataDirectory();

    // Initialize aggregators for all supported timeframes
    this.initializeAggregators();

    // Load historical data
    this.loadHistoricalData();

    console.log('CandleAggregatorService initialized with timeframes:', this.supportedTimeframes);
  }

  saveHistoricalData() {
    for (const [timeframe, aggregator] of this.aggregators) {
      const filePath = path.join(this.dataDir, `${timeframe}.json`);
      const candles = aggregator.getAllCandles();
      fs.writeFileSync(filePath, JSON.stringify(candles, null, 2));
    }
  }

  loadHistoricalData() {
    for (const timeframe of this.supportedTimeframes) {
      const filePath = path.join(this.dataDir, `${timeframe}.json`);
      
      if (fs.existsSync(filePath)) {
        try {
          const data = fs.readFileSync(filePath, 'utf-8');
          const candles = JSON.parse(data);
          const aggregator = this.aggregators.get(timeframe);
          aggregator.completedCandles = candles;
          console.log(`Loaded ${candles.length} historical candles for ${timeframe}`);
        } catch (error) {
          console.error(`Error loading historical data for ${timeframe}:`, error);
        }
      }
    }
  }

  ensureDataDirectory() {
    if (!fs.existsSync(this.dataDir)) {
      fs.mkdirSync(this.dataDir, { recursive: true });
      console.log(`Created data directory: ${this.dataDir}`);
    }
  }

  // Initialize aggregators for all supported timeframes
  initializeAggregators() {
    for (const timeframe of this.supportedTimeframes) {
      this.aggregators.set(timeframe, new CandleAggregator(timeframe));
    }
  }

  // Update all aggregators with new price data
  updatePrice(price, timestamp = Date.now()) {
    const numericPrice = parseFloat(price);
    if (isNaN(numericPrice) || numericPrice <= 0) {
      console.warn(`CandleAggregatorService: Invalid price ${price}`);
      return;
    }

    // Update all timeframe aggregators
    for (const [timeframe, aggregator] of this.aggregators) {
      try {
        aggregator.addPrice(numericPrice, timestamp);
        this.saveHistoricalData();
      } catch (error) {
        console.error(`Error updating ${timeframe} aggregator:`, error);
      }
    }
  }

  // Get candles for a specific timeframe
  getCandles(timeframe, count = null, includeCurrent = false) {
    const aggregator = this.aggregators.get(timeframe);
    if (!aggregator) {
      console.warn(`CandleAggregatorService: Unsupported timeframe ${timeframe}`);
      return [];
    }

    const candles = includeCurrent ?
      aggregator.getAllCandlesIncludingCurrent() :
      aggregator.getAllCandles();

    if (count && count > 0) {
      return candles.slice(-count);
    }

    return candles;
  }

  // Get candles for a specific timeframe within a time period
  getCandlesInPeriod(timeframe, period = '1d', includeCurrent = true) {
    const candles = this.getCandles(timeframe, null, includeCurrent);

    if (candles.length === 0) {
      return [];
    }

    const now = Date.now();
    let cutoffTime;

    switch (period) {
      case '1h': cutoffTime = now - (60 * 60 * 1000); break;
      case '6h': cutoffTime = now - (6 * 60 * 60 * 1000); break;
      case '1d': cutoffTime = now - (24 * 60 * 60 * 1000); break;
      case '3d': cutoffTime = now - (3 * 24 * 60 * 60 * 1000); break;
      case '1w': cutoffTime = now - (7 * 24 * 60 * 60 * 1000); break;
      default: cutoffTime = now - (24 * 60 * 60 * 1000);
    }

    return candles.filter(candle => candle.startTime >= cutoffTime);
  }

  // Get the latest candle for a timeframe
  getLatestCandle(timeframe) {
    const candles = this.getCandles(timeframe);
    return candles.length > 0 ? candles[candles.length - 1] : null;
  }

  // Get the current forming candle for a timeframe
  getCurrentCandle(timeframe) {
    const aggregator = this.aggregators.get(timeframe);
    if (!aggregator) {
      return null;
    }
    return aggregator.currentCandle ? { ...aggregator.currentCandle } : null;
  }

  // Get current price (close of latest 1m candle)
  getCurrentPrice() {
    const latestCandle = this.getLatestCandle('1m');
    return latestCandle ? latestCandle.close : null;
  }

  // Calculate Simple Moving Average for a timeframe
  getSMA(timeframe, period) {
    const candles = this.getCandles(timeframe);
    
    if (candles.length < period) {
      return null;
    }
    
    const recentCandles = candles.slice(-period);
    const sum = recentCandles.reduce((acc, candle) => acc + candle.close, 0);
    return sum / period;
  }

  // Calculate highest high over a period
  getHighest(timeframe, period) {
    const candles = this.getCandles(timeframe);
    
    if (candles.length < period) {
      return null;
    }
    
    const recentCandles = candles.slice(-period);
    return Math.max(...recentCandles.map(candle => candle.high));
  }

  // Calculate lowest low over a period
  getLowest(timeframe, period) {
    const candles = this.getCandles(timeframe);
    
    if (candles.length < period) {
      return null;
    }
    
    const recentCandles = candles.slice(-period);
    return Math.min(...recentCandles.map(candle => candle.low));
  }

  // Calculate Average True Range (ATR)
  getATR(timeframe, period) {
    const candles = this.getCandles(timeframe);
    
    if (candles.length < period + 1) {
      return null;
    }
    
    const trueRanges = [];
    
    for (let i = 1; i < candles.length; i++) {
      const current = candles[i];
      const previous = candles[i - 1];
      
      const tr1 = current.high - current.low;
      const tr2 = Math.abs(current.high - previous.close);
      const tr3 = Math.abs(current.low - previous.close);
      
      trueRanges.push(Math.max(tr1, tr2, tr3));
    }
    
    if (trueRanges.length < period) {
      return null;
    }
    
    const recentTR = trueRanges.slice(-period);
    return recentTR.reduce((acc, tr) => acc + tr, 0) / period;
  }

  // Get service statistics
  getStats() {
    const stats = {
      supportedTimeframes: this.supportedTimeframes,
      aggregators: {}
    };
    
    for (const [timeframe, aggregator] of this.aggregators) {
      const candles = aggregator.getAllCandles();
      stats.aggregators[timeframe] = {
        candleCount: candles.length,
        latestCandle: candles.length > 0 ? candles[candles.length - 1] : null,
        oldestCandle: candles.length > 0 ? candles[0] : null
      };
    }
    
    return stats;
  }

  // Clear all candle data (for testing/reset)
  clearAll() {
    for (const aggregator of this.aggregators.values()) {
      aggregator.candles = [];
      aggregator.currentCandle = null;
    }
    console.log('CandleAggregatorService: All candle data cleared');
  }

  // Get candles in Chart.js financial format
  getCandlesForChart(timeframe, period = '1d', includeCurrent = true) {
    const completedCandles = this.getCandlesInPeriod(timeframe, period, false); // Get only completed candles
    const currentCandle = this.getCurrentCandle(timeframe);

    const result = [];

    // Add completed candles (not forming)
    completedCandles.forEach(candle => {
      result.push({
        t: candle.startTime,
        o: candle.open,
        h: candle.high,
        l: candle.low,
        c: candle.close,
        v: candle.volume,
        isForming: false
      });
    });

    // Add current forming candle if requested and exists
    if (includeCurrent && currentCandle) {
      result.push({
        t: currentCandle.startTime,
        o: currentCandle.open,
        h: currentCandle.high,
        l: currentCandle.low,
        c: currentCandle.close,
        v: currentCandle.volume,
        isForming: true
      });
    }

    return result;
  }

  // Check if we have sufficient data for analysis
  hasSufficientData(timeframe, minCandles = 20) {
    const candles = this.getCandles(timeframe);
    return candles.length >= minCandles;
  }
}

// Singleton instance
let candleAggregatorService = null;

export function getCandleAggregatorService() {
  if (!candleAggregatorService) {
    candleAggregatorService = new CandleAggregatorService();
  }
  return candleAggregatorService;
}

// Export singleton for direct use
export const CandleService = getCandleAggregatorService();
