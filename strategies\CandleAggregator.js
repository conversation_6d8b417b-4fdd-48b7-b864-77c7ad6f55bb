// Time-based candle aggregation system for proper technical analysis
export class CandleAggregator {
  constructor(timeframe = '5m') {
    this.timeframe = timeframe;
    this.timeframeMs = this.parseTimeframe(timeframe);
    this.currentCandle = null;
    this.completedCandles = [];
    this.maxCandles = 200; // Keep last 200 candles
  }

  // Parse timeframe string to milliseconds
  parseTimeframe(timeframe) {
    const timeframes = {
      '1m': 60 * 1000,           // 1 minute
      '5m': 5 * 60 * 1000,       // 5 minutes
      '15m': 15 * 60 * 1000,     // 15 minutes
      '30m': 30 * 60 * 1000,     // 30 minutes
      '1h': 60 * 60 * 1000,      // 1 hour
      '4h': 4 * 60 * 60 * 1000,  // 4 hours
      '1d': 24 * 60 * 60 * 1000  // 1 day
    };
    
    return timeframes[timeframe] || timeframes['5m'];
  }

  // Get candle start time for a given timestamp
  getCandleStartTime(timestamp) {
    return Math.floor(timestamp / this.timeframeMs) * this.timeframeMs;
  }

  // Add a new price update
  addPrice(price, timestamp = Date.now()) {
    const candleStartTime = this.getCandleStartTime(timestamp);
    
    // If this is a new candle period
    if (!this.currentCandle || this.currentCandle.startTime !== candleStartTime) {
      // Complete the previous candle if it exists
      if (this.currentCandle) {
        this.completeCandle();
      }
      
      // Start a new candle
      this.currentCandle = {
        startTime: candleStartTime,
        endTime: candleStartTime + this.timeframeMs,
        open: price,
        high: price,
        low: price,
        close: price,
        volume: 1, // Count of price updates
        lastUpdate: timestamp
      };
    } else {
      // Update the current candle
      this.currentCandle.high = Math.max(this.currentCandle.high, price);
      this.currentCandle.low = Math.min(this.currentCandle.low, price);
      this.currentCandle.close = price;
      this.currentCandle.volume += 1;
      this.currentCandle.lastUpdate = timestamp;
    }
  }

  // Complete the current candle and add it to history
  completeCandle() {
    if (this.currentCandle) {
      this.completedCandles.push({ ...this.currentCandle });
      
      // Limit history size
      if (this.completedCandles.length > this.maxCandles) {
        this.completedCandles = this.completedCandles.slice(-this.maxCandles);
      }
    }
  }

  // Get all candles (completed + current if exists)
  getAllCandles() {
    const candles = [...this.completedCandles];
    if (this.currentCandle) {
      candles.push({ ...this.currentCandle });
    }
    return candles;
  }

  // Get completed candles only
  getCompletedCandles() {
    return [...this.completedCandles];
  }

  // Get closing prices for technical analysis
  getClosingPrices(count = null) {
    const candles = this.getCompletedCandles();
    const prices = candles.map(candle => candle.close);
    
    if (count && count > 0) {
      return prices.slice(-count);
    }
    
    return prices;
  }

  // Get high prices
  getHighPrices(count = null) {
    const candles = this.getCompletedCandles();
    const prices = candles.map(candle => candle.high);
    
    if (count && count > 0) {
      return prices.slice(-count);
    }
    
    return prices;
  }

  // Get low prices
  getLowPrices(count = null) {
    const candles = this.getCompletedCandles();
    const prices = candles.map(candle => candle.low);
    
    if (count && count > 0) {
      return prices.slice(-count);
    }
    
    return prices;
  }

  // Get typical prices (HLC/3)
  getTypicalPrices(count = null) {
    const candles = this.getCompletedCandles();
    const prices = candles.map(candle => (candle.high + candle.low + candle.close) / 3);
    
    if (count && count > 0) {
      return prices.slice(-count);
    }
    
    return prices;
  }

  // Check if we have enough candles for analysis
  hasEnoughCandles(requiredCount) {
    return this.completedCandles.length >= requiredCount;
  }

  // Get the highest high over a period
  getHighestHigh(periods) {
    if (!this.hasEnoughCandles(periods)) {
      return null;
    }
    
    const highs = this.getHighPrices(periods);
    return Math.max(...highs);
  }

  // Get the lowest low over a period
  getLowestLow(periods) {
    if (!this.hasEnoughCandles(periods)) {
      return null;
    }
    
    const lows = this.getLowPrices(periods);
    return Math.min(...lows);
  }

  // Calculate Simple Moving Average
  calculateSMA(periods) {
    if (!this.hasEnoughCandles(periods)) {
      return null;
    }
    
    const prices = this.getClosingPrices(periods);
    const sum = prices.reduce((acc, price) => acc + price, 0);
    return sum / periods;
  }

  // Calculate True Range for ATR
  calculateTrueRange() {
    const candles = this.getCompletedCandles();
    if (candles.length < 2) {
      return [];
    }
    
    const trueRanges = [];
    for (let i = 1; i < candles.length; i++) {
      const current = candles[i];
      const previous = candles[i - 1];
      
      const tr1 = current.high - current.low;
      const tr2 = Math.abs(current.high - previous.close);
      const tr3 = Math.abs(current.low - previous.close);
      
      trueRanges.push(Math.max(tr1, tr2, tr3));
    }
    
    return trueRanges;
  }

  // Calculate Average True Range
  calculateATR(periods = 14) {
    const trueRanges = this.calculateTrueRange();
    if (trueRanges.length < periods) {
      return null;
    }
    
    const recentTR = trueRanges.slice(-periods);
    const sum = recentTR.reduce((acc, tr) => acc + tr, 0);
    return sum / periods;
  }

  // Get current price (close of current or last candle)
  getCurrentPrice() {
    if (this.currentCandle) {
      return this.currentCandle.close;
    }
    
    if (this.completedCandles.length > 0) {
      return this.completedCandles[this.completedCandles.length - 1].close;
    }
    
    return null;
  }

  // Get timeframe description
  getTimeframeDescription() {
    const descriptions = {
      '1m': '1-minute',
      '5m': '5-minute',
      '15m': '15-minute',
      '30m': '30-minute',
      '1h': '1-hour',
      '4h': '4-hour',
      '1d': 'daily'
    };
    
    return descriptions[this.timeframe] || this.timeframe;
  }

  // Get status information
  getStatus() {
    return {
      timeframe: this.timeframe,
      timeframeDescription: this.getTimeframeDescription(),
      completedCandles: this.completedCandles.length,
      currentCandle: this.currentCandle ? {
        startTime: new Date(this.currentCandle.startTime).toISOString(),
        open: this.currentCandle.open,
        high: this.currentCandle.high,
        low: this.currentCandle.low,
        close: this.currentCandle.close,
        volume: this.currentCandle.volume
      } : null,
      lastUpdate: this.currentCandle ? new Date(this.currentCandle.lastUpdate).toISOString() : null
    };
  }
}
