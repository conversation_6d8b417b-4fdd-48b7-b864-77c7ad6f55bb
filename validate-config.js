#!/usr/bin/env node

// CLI tool to validate and fix bot configuration
import { validateConfigFile } from './config-validator.js';
import { program } from 'commander';

program
  .name('validate-config')
  .description('Validate and fix bot configuration file')
  .version('1.0.0')
  .option('-f, --file <path>', 'Config file path', 'bot-config.json')
  .option('-d, --dry-run', 'Show what would be fixed without making changes')
  .option('-v, --verbose', 'Show detailed validation information')
  .parse();

const options = program.opts();

async function main() {
  console.log(`🔍 Validating configuration file: ${options.file}\n`);
  
  try {
    if (options.dryRun) {
      console.log('🔬 DRY RUN MODE - No changes will be made\n');
      // TODO: Implement dry run mode
      console.log('Dry run mode not yet implemented. Use --verbose for detailed info.');
      return;
    }
    
    const result = await validateConfigFile(options.file);
    
    if (options.verbose) {
      console.log('\n📋 Detailed Validation Results:');
      console.log('================================');
      console.log(`Valid: ${result.isValid}`);
      console.log(`Errors: ${result.errors.length}`);
      console.log(`Fixes Applied: ${result.fixes.length}`);
      
      if (result.errors.length > 0) {
        console.log('\n❌ Errors Found:');
        result.errors.forEach(error => {
          console.log(`  - ${error}`);
        });
      }
      
      if (result.fixes.length > 0) {
        console.log('\n🔧 Fixes Applied:');
        result.fixes.forEach(fix => {
          console.log(`  ${fix.strategy}:`);
          fix.fixes.forEach(f => {
            console.log(`    - ${f}`);
          });
        });
      }
      
      console.log('\n📊 Configuration Summary:');
      console.log(`Strategies: ${result.config.portfolio?.strategies?.length || 0}`);
      console.log(`Max Trading Balance: ${result.config.maxTradingBalance ? '$' + result.config.maxTradingBalance : 'Unlimited'}`);
      
      if (result.config.portfolio?.strategies) {
        console.log('\nStrategy Allocations:');
        result.config.portfolio.strategies.forEach(strategy => {
          console.log(`  ${strategy.name}: ${(strategy.allocation * 100).toFixed(1)}%`);
        });
      }
    }
    
    if (result.fixes.length === 0) {
      console.log('✅ Configuration is already valid - no fixes needed!');
    } else {
      console.log(`✅ Configuration validated and ${result.fixes.length} fixes applied!`);
      console.log(`📁 Updated configuration saved to: ${options.file}`);
    }
    
  } catch (error) {
    console.error('❌ Error validating configuration:', error.message);
    process.exit(1);
  }
}

main();
