// bot.js
import { RobinhoodAPI } from './robinhoodClient.js';
import { v4 as uuidv4 } from 'uuid';
import Big from 'big.js';
import { createStrategy } from './strategies/index.js';
import { validateConfigFile } from './config-validator.js';
import { getCandleAggregatorService } from './services/CandleAggregatorService.js';
import fs from 'fs';
import path from 'path';

const SYMBOL = 'BTC-USD';
let botRunning = false;
let currentStopLoss = null;
let logs = [];
let tradeCount = 0;
let startingBalance = new Big(0);
let dailyStartBalance = new Big(0);
let maxTradingBalance = null; // null means use all available balance
const MAX_LOGS = 1000; // Define MAX_LOGS constant

const CONFIG_FILE = 'bot-config.json';

// Initialize centralized candle aggregator service
const candleService = getCandleAggregatorService();



// Strategy configuration - always use portfolio manager
let currentStrategy = createStrategy('portfolio-manager', {
  strategies: [
    {
      name: 'trend-following',
      allocation: 0.4,
      config: { accountPercentToUse: 1.0, minTrendLookback: 20, volatilityLookback: 14 }
    },
    {
      name: 'mean-reversion',
      allocation: 0.3,
      config: { accountPercentToUse: 1.0, lookbackPeriod: 20, deviationThreshold: 2.0 }
    },
    {
      name: 'breakout',
      allocation: 0.3,
      config: { accountPercentToUse: 1.0, breakoutLookback: 20, confirmRetest: false }
    }
  ],
  rebalanceFrequency: ********,
  performanceWindow: *********,
  minAllocation: 0.1,
  maxAllocation: 0.6,
  rebalanceThreshold: 0.1
});

function log(message) {
  const timestamp = new Date().toISOString();
  const logEntry = `[${timestamp}] ${message}`;
  console.log(logEntry);
  
  logs.push(logEntry);
  
  // Keep only the last MAX_LOGS entries
  if (logs.length > MAX_LOGS) {
    logs = logs.slice(-MAX_LOGS);
  }
}

export function setStrategy(strategyName, config = {}) {
  try {
    currentStrategy = createStrategy(strategyName, config);
    log(`Strategy changed to: ${currentStrategy.getName()}`);
    saveConfig(); // Save after strategy change
    return { success: true, strategy: currentStrategy.getName() };
  } catch (error) {
    log(`Failed to set strategy: ${error.message}`);
    return { success: false, error: error.message };
  }
}

export function getCurrentStrategy() {
  return currentStrategy;
}

export function getBotStatus() {
  return {
    running: botRunning,
    trades: tradeCount,
    logs: logs.slice(-50),
    currentStrategy: currentStrategy.getName()
  };
}

export async function getBotStats() {
  try {
    const account = await RobinhoodAPI.getAccount();
    if (!account) {
      throw new Error('Failed to fetch account data');
    }

    const holdings = await RobinhoodAPI.getHoldings();
    
    let btcHolding = null;
    if (Array.isArray(holdings)) {
      btcHolding = holdings.find(h => h.asset_code === 'BTC');
    } else if (holdings && holdings.results && Array.isArray(holdings.results)) {
      btcHolding = holdings.results.find(h => h.asset_code === 'BTC');
    }
    
    const best = await RobinhoodAPI.getBestBidAsk(SYMBOL);
    let currentPrice = new Big(0);
    
    // Handle the response format according to the API documentation
    if (best && best.results && best.results.length > 0) {
      const priceData = best.results[0];
      // Use ask price for buying, bid price for selling
      currentPrice = new Big(priceData.price || priceData.ask_inclusive_of_buy_spread || '0');
    }
    
    const btcHoldings = new Big(btcHolding?.total_quantity ?? '0');
    const btcValue = btcHoldings.mul(currentPrice);
    const cashBalance = new Big(account.buying_power ?? account.cash?.total_available ?? '0');
    const portfolioValue = cashBalance.add(btcValue);
    
    const pnl = portfolioValue.minus(dailyStartBalance.gt(0) ? dailyStartBalance : startingBalance);
    
    return {
      botRunning,
      trades: tradeCount,
      balance: cashBalance.toFixed(2),
      btcHoldings: btcHoldings.toFixed(8),
      btcPrice: currentPrice.toFixed(2),
      portfolioValue: portfolioValue.toFixed(2),
      pnl: pnl.toFixed(2),
      stopLoss: currentStopLoss ? await getStopLossPrice() : null,
      logs: logs.slice(-50),
      currentStrategy: currentStrategy.getName(),
      maxTradingBalance: maxTradingBalance
    };
  } catch (error) {
    log(`Error getting stats: ${error.message}`);
    return {
      botRunning,
      trades: tradeCount,
      balance: '0.00',
      btcHoldings: '0.********',
      btcPrice: '0.00',
      portfolioValue: '0.00',
      pnl: '0.00',
      stopLoss: null,
      logs: logs.slice(-50),
      currentStrategy: currentStrategy.getName(),
      maxTradingBalance: maxTradingBalance,
      error: error.message
    };
  }
}

async function getStopLossPrice() {
  try {
    const order = await RobinhoodAPI.getOrder(currentStopLoss);
    return order.stop_price;
  } catch (error) {
    return null;
  }
}

export async function startBot() {
  if (botRunning) return;
  botRunning = true;
  log('Bot started.');

  const account = await RobinhoodAPI.getAccount();
  const usd = new Big(account.cash.total_available);
  startingBalance = usd;

  // Set daily start balance if not set
  if (dailyStartBalance.eq(0)) {
    const positions = await RobinhoodAPI.getPositions();
    const btcPosition = positions.find(p => p.symbol === SYMBOL);
    const btcHoldings = new Big(btcPosition?.quantity ?? '0');
    const best = await RobinhoodAPI.getBestBidAsk(SYMBOL);
    const currentPrice = new Big(best[0]?.bid_price ?? '0');
    dailyStartBalance = usd.add(btcHoldings.mul(currentPrice));
  }

  // Validate positions against Robinhood API
  await validatePositions();

  loop();
}

export async function stopBot() {
  if (!botRunning) return;
  botRunning = false;

  // Save OHLC data before stopping
  try {
    candleService.forceSave();
    log('💾 OHLC data saved before shutdown');
  } catch (error) {
    log(`⚠️ Failed to save OHLC data on shutdown: ${error.message}`);
  }

  log('Bot stopped.');
  if (currentStopLoss) {
    await RobinhoodAPI.cancelOrder(currentStopLoss);
    log(`Canceled trailing stop-loss order: ${currentStopLoss}`);
  }
}

let priceHistory = [];
const MAX_PRICE_HISTORY = 500; // Keep last 500 price points

async function loop() {
  while (botRunning) {
    try {
      // Get current price first
      const best = await RobinhoodAPI.getBestBidAsk(SYMBOL);
      const bid = new Big(best[0]?.bid_price ?? '0');

      if (bid.eq(0)) {
        log('No price data available, waiting...');
        await delay(30000);
        continue;
      }

      const currentPrice = parseFloat(bid.toString());
      
      // Add to price history with memory management
      priceHistory.push(currentPrice);
      if (priceHistory.length > MAX_PRICE_HISTORY) {
        priceHistory = priceHistory.slice(-MAX_PRICE_HISTORY);
      }
      
      // Update centralized candle service with current price
      try {
        candleService.updatePrice(currentPrice);
        console.log(`[DEBUG] Updated candle service with price: $${currentPrice}`);
      } catch (error) {
        console.error(`[ERROR] Failed to update candle service:`, error);
      }

      // Update strategy with current price
      currentStrategy.updatePrice(currentPrice);

      // Get holdings
      const holdings = await RobinhoodAPI.getHoldings();
      await delay(1000);
      
      let btcHolding = null;
      if (holdings && holdings.results && Array.isArray(holdings.results)) {
        btcHolding = holdings.results.find(h => h.asset_code === 'BTC');
      }
      const btc = new Big(btcHolding?.total_quantity ?? '0');

      // Get effective balance for trading
      const account = await RobinhoodAPI.getAccount();
      const usdAvailable = new Big(account.cash?.total_available ?? '0');
      const effectiveBalance = getEffectiveBalance(parseFloat(usdAvailable.toString()));

      // Handle multi-strategy trading logic
      await handleMultiStrategyTrading(currentPrice, holdings, effectiveBalance);

      // Handle existing positions and stop losses
      if (!btc.eq(0)) {
        await handleMultiStrategyStopLosses(currentPrice, btc, holdings);
        await delay(30000);
      }
    } catch (error) {
      log(`Error in bot loop: ${error.message}`);
      await delay(30000);
    }
  }
}

function delay(ms) {
  return new Promise((res) => setTimeout(res, ms));
}

// Handle multi-strategy trading logic
async function handleMultiStrategyTrading(currentPrice, holdings, effectiveBalance) {
  if (effectiveBalance < 10) {
    log(`Insufficient effective balance for trading: $${effectiveBalance.toFixed(2)}`);
    return;
  }

  // Check if any strategies want to enter trades
  const entryDecision = currentStrategy.shouldEnterTrade(currentPrice, {
    holdings,
    availableBalance: effectiveBalance
  });

  if (entryDecision.shouldEnter) {
    await executeStrategyBuy(entryDecision, currentPrice, effectiveBalance);
  }

  // Check if any strategies want to exit positions
  if (currentStrategy.getStrategiesNeedingExit) {
    const exitDecisions = currentStrategy.getStrategiesNeedingExit(currentPrice);
    for (const exitDecision of exitDecisions) {
      await executeStrategyExit(exitDecision, currentPrice);
    }
  }
}

// Execute a buy order for a specific strategy
async function executeStrategyBuy(entryDecision, currentPrice, effectiveBalance) {
  try {
    const positionSize = currentStrategy.getPositionSize(
      effectiveBalance,
      currentPrice,
      entryDecision.strategy
    );
    const qty = positionSize.quantity.toFixed(8);

    log(`${entryDecision.reason}! Attempting to BUY ${qty} BTC at ${currentPrice.toFixed(2)} (${positionSize.reason}, max: $${effectiveBalance.toFixed(2)})`);

    const res = await RobinhoodAPI.placeOrder(
      uuidv4(),
      'buy',
      'market',
      SYMBOL,
      { asset_quantity: qty }
    );

    if (res) {
      log(`BUY order placed: ${qty} BTC at ${currentPrice.toFixed(2)} for strategy: ${entryDecision.strategy}`);
      tradeCount++;

      // Record trade in portfolio manager
      if (currentStrategy.recordTrade) {
        currentStrategy.recordTrade('buy', currentPrice, parseFloat(qty), entryDecision.strategy);
      }
    } else {
      log('Failed to place BUY order');
    }
  } catch (error) {
    log(`Error executing buy for strategy ${entryDecision.strategy}: ${error.message}`);
  }
}

// Execute a sell order for a specific strategy
async function executeStrategyExit(exitDecision, currentPrice) {
  try {
    const qty = exitDecision.quantityToSell.toFixed(8);

    log(`${exitDecision.decision.reason}! Attempting to SELL ${qty} BTC at ${currentPrice.toFixed(2)} for strategy: ${exitDecision.strategy}`);

    const res = await RobinhoodAPI.placeOrder(
      uuidv4(),
      'sell',
      'market',
      SYMBOL,
      { asset_quantity: qty }
    );

    if (res) {
      log(`SELL order placed: ${qty} BTC at ${currentPrice.toFixed(2)} for strategy: ${exitDecision.strategy}`);
      tradeCount++;

      // Record trade in portfolio manager
      if (currentStrategy.recordTrade) {
        currentStrategy.recordTrade('sell', currentPrice, parseFloat(qty), exitDecision.strategy);
      }
    } else {
      log('Failed to place SELL order');
    }
  } catch (error) {
    log(`Error executing sell for strategy ${exitDecision.strategy}: ${error.message}`);
  }
}

// Handle stop losses for multiple strategies
async function handleMultiStrategyStopLosses(currentPrice, totalBtc, holdings) {
  // Note: Due to Robinhood API limitations, we use a single stop loss for the total position
  // In a more advanced implementation, each strategy would have its own stop loss orders

  // Cancel existing stop loss
  if (currentStopLoss) {
    await RobinhoodAPI.cancelOrder(currentStopLoss);
    await delay(1000);
  }

  // Get stop loss decisions from all strategies with positions
  const stopLossDecisions = [];

  if (currentStrategy.strategyInstances) {
    currentStrategy.strategyInstances.forEach((strategy, name) => {
      const position = currentStrategy.getStrategyPosition(name);
      if (position.quantity > 0) {
        const stopLoss = currentStrategy.calculateStrategyStopLoss(name, currentPrice, { holdings });
        if (stopLoss) {
          stopLossDecisions.push(stopLoss);
        }
      }
    });
  }

  if (stopLossDecisions.length > 0) {
    // Calculate weighted average stop price based on position sizes
    let totalValue = 0;
    let weightedStopPrice = 0;

    stopLossDecisions.forEach(decision => {
      const positionValue = decision.quantity * currentPrice;
      totalValue += positionValue;
      weightedStopPrice += decision.stopPrice * positionValue;
    });

    const avgStopPrice = totalValue > 0 ? (weightedStopPrice / totalValue) : currentPrice * 0.95;
    const stopPrice = avgStopPrice.toFixed(2);

    log(`Placing portfolio stop: ${totalBtc.toFixed(8)} BTC at ${stopPrice} (weighted average from ${stopLossDecisions.length} strategies)`);

    const res = await RobinhoodAPI.placeOrder(
      uuidv4(),
      'sell',
      'stop_loss',
      SYMBOL,
      { stop_price: stopPrice, asset_quantity: totalBtc.toFixed(8) }
    );

    if (res && res.id) {
      currentStopLoss = res.id;
      log(`Portfolio stop loss placed at ${stopPrice} (Order ID: ${currentStopLoss})`);

      // Track the stop loss in portfolio manager
      if (currentStrategy.updateStrategyStopLoss) {
        // For now, assign to the strategy with the largest position
        const largestStrategy = stopLossDecisions.reduce((max, current) =>
          current.quantity > max.quantity ? current : max
        );
        currentStrategy.updateStrategyStopLoss(largestStrategy.strategy, res.id, parseFloat(stopPrice));
      }
    } else {
      log('Failed to place portfolio stop loss order');
    }
  }
}

// Technical analysis helpers
function calculateSMA(prices, period) {
  if (prices.length < period) return null;
  const sum = prices.slice(-period).reduce((a, b) => a + b, 0);
  return sum / period;
}

function calculateATR(prices, period = 14) {
  if (prices.length < period + 1) return BASE_STOP_LOSS_PERCENT;
  
  let atr = 0;
  for (let i = prices.length - period; i < prices.length - 1; i++) {
    const high = Math.max(prices[i], prices[i + 1]);
    const low = Math.min(prices[i], prices[i + 1]);
    const trueRange = high - low;
    atr += trueRange;
  }
  
  const avgTrueRange = atr / period;
  const currentPrice = prices[prices.length - 1];
  
  // Convert ATR to percentage and cap it
  const atrPercent = (avgTrueRange / currentPrice) * 2; // 2x ATR for stop
  return Math.min(Math.max(atrPercent, 0.015), 0.08); // Between 1.5% and 8%
}

function isUptrend(prices, period = MIN_TREND_LOOKBACK) {
  if (prices.length < period) return true; // Default to true if not enough data
  
  const sma = calculateSMA(prices, period);
  const currentPrice = prices[prices.length - 1];
  
  // Price above SMA and SMA is rising
  const olderSma = calculateSMA(prices.slice(0, -5), period);
  return currentPrice > sma && sma > olderSma;
}

function shouldEnterTrade(currentPrice) {
  if (priceHistory.length < MIN_TREND_LOOKBACK) {
    log('Not enough price history for analysis, waiting...');
    return false;
  }
  
  // Check if we're in an uptrend
  if (!isUptrend(priceHistory)) {
    log('Market not in uptrend, waiting for better conditions');
    return false;
  }
  
  // Check if price is above short-term moving average
  const shortSMA = calculateSMA(priceHistory, 5);
  if (currentPrice < shortSMA) {
    log('Price below short-term average, waiting for pullback to end');
    return false;
  }
  
  // Additional filter: avoid buying at recent highs
  const recentHigh = Math.max(...priceHistory.slice(-10));
  if (currentPrice > recentHigh * 0.98) {
    log('Price too close to recent high, waiting for better entry');
    return false;
  }
  
  return true;
}

export function setMaxTradingBalance(amount) {
  try {
    const oldMaxBalance = maxTradingBalance;
    maxTradingBalance = parseFloat(amount);
    log(`Max trading balance set to: $${maxTradingBalance.toFixed(2)}`);

    // Handle position reconciliation if balance is reduced
    if (botRunning && currentStrategy.reconcilePositionsForBalanceChange) {
      handleMaxBalanceChange(oldMaxBalance, maxTradingBalance);
    }

    saveConfig(); // Save after balance change
    return { success: true, maxBalance: maxTradingBalance };
  } catch (error) {
    log(`Failed to set max trading balance: ${error.message}`);
    return { success: false, error: error.message };
  }
}

export function getMaxTradingBalance() {
  return maxTradingBalance;
}

// Export candle service for API access
export function getCandleService() {
  return candleService;
}

function getEffectiveBalance(availableBalance) {
  if (maxTradingBalance === null) {
    return availableBalance;
  }
  return Math.min(availableBalance, maxTradingBalance);
}

// Handle max trading balance changes and position reconciliation
async function handleMaxBalanceChange(oldMaxBalance, newMaxBalance) {
  try {
    // Get current price for calculations
    const best = await RobinhoodAPI.getBestBidAsk(SYMBOL);
    const currentPrice = parseFloat(best[0]?.bid_price ?? '0');

    if (currentPrice === 0) {
      log('Cannot reconcile positions: no current price available');
      return;
    }

    // Get reconciliation plan from portfolio manager
    const reconciliationPlan = currentStrategy.reconcilePositionsForBalanceChange(newMaxBalance, currentPrice);

    if (reconciliationPlan.action === 'none') {
      log(reconciliationPlan.message);
      return;
    }

    if (reconciliationPlan.action === 'reduce') {
      log(`Position reconciliation needed: ${reconciliationPlan.message}`);

      // Execute reduction plan
      for (const reduction of reconciliationPlan.reductionPlan) {
        if (reduction.quantityToSell > 0.00001) {
          log(`Reducing ${reduction.strategy} position: selling ${reduction.quantityToSell.toFixed(8)} BTC (${reduction.reason})`);

          const res = await RobinhoodAPI.placeOrder(
            uuidv4(),
            'sell',
            'market',
            SYMBOL,
            { asset_quantity: reduction.quantityToSell.toFixed(8) }
          );

          if (res) {
            log(`Reduction order placed: ${reduction.quantityToSell.toFixed(8)} BTC for ${reduction.strategy}`);
            tradeCount++;

            // Record the trade
            if (currentStrategy.recordTrade) {
              currentStrategy.recordTrade('sell', currentPrice, reduction.quantityToSell, reduction.strategy);
            }

            // Small delay between orders
            await delay(2000);
          } else {
            log(`Failed to place reduction order for ${reduction.strategy}`);
          }
        }
      }

      log(`Position reconciliation completed. Total reduction: $${reconciliationPlan.totalReduction.toFixed(2)}`);
    }
  } catch (error) {
    log(`Error during position reconciliation: ${error.message}`);
  }
}

// Load saved configuration on startup with validation
async function loadConfig() {
  try {
    log('🔍 Validating and loading configuration...');

    // Validate and fix config file
    const validationResult = await validateConfigFile(CONFIG_FILE);

    if (!validationResult.isValid) {
      log('⚠️ Configuration had errors, but fixes were applied');
    }

    if (validationResult.fixes.length > 0) {
      log(`🔧 Applied ${validationResult.fixes.length} configuration fixes`);
      validationResult.fixes.forEach(fix => {
        log(`  ${fix.strategy}: ${fix.fixes.length} fixes applied`);
      });
    }

    const config = validationResult.config;

    // Restore max trading balance
    if (config.maxTradingBalance !== undefined) {
      maxTradingBalance = config.maxTradingBalance;
      log(`Restored max trading balance: $${maxTradingBalance?.toFixed(2) || 'unlimited'}`);
    }

    // Restore portfolio configuration
    if (config.portfolio && config.portfolio.strategies.length > 0) {
      try {
        currentStrategy = createStrategy('portfolio-manager', config.portfolio);
        log(`✅ Restored portfolio with ${config.portfolio.strategies.length} strategies:`);
        config.portfolio.strategies.forEach(s => {
          log(`  - ${s.name}: ${(s.allocation * 100).toFixed(1)}% allocation ${s.enabled === false ? '(disabled)' : ''}`);
        });

        // Restore positions and trade history
        if (config.positions) {
          restorePositions(config.positions);
        }
        if (config.tradeHistory) {
          restoreTradeHistory(config.tradeHistory);
        }
      } catch (error) {
        log(`Failed to restore portfolio: ${error.message}, using default`);
        await createDefaultPortfolio();
      }
    }
    // Legacy support for old config format
    else if (config.strategy && config.strategy.name === 'portfolio-manager') {
      try {
        currentStrategy = createStrategy('portfolio-manager', config.strategy.config);
        log(`Restored legacy portfolio configuration`);
      } catch (error) {
        log(`Failed to restore legacy portfolio: ${error.message}, using default`);
        await createDefaultPortfolio();
      }
    } else {
      log('No valid portfolio configuration found, creating default');
      await createDefaultPortfolio();
    }

    log('✅ Configuration loaded and validated successfully');
  } catch (error) {
    log(`❌ Failed to load config: ${error.message}`);
    log('Using fallback default configuration');
    await createDefaultPortfolio();
  }
}

// Create default portfolio configuration
async function createDefaultPortfolio() {
  const defaultPortfolio = {
    strategies: [
      {
        name: 'trend-following',
        allocation: 0.4,
        config: { accountPercentToUse: 1.0 }
      },
      {
        name: 'mean-reversion',
        allocation: 0.3,
        config: { accountPercentToUse: 1.0 }
      },
      {
        name: 'breakout',
        allocation: 0.3,
        config: { accountPercentToUse: 1.0 }
      }
    ]
  };

  try {
    currentStrategy = createStrategy('portfolio-manager', defaultPortfolio);
    log('Created default portfolio with 3 strategies');

    // Save the default configuration
    saveConfig();
  } catch (error) {
    log(`Failed to create default portfolio: ${error.message}`);
    // Ultimate fallback
    currentStrategy = createStrategy('simple', { accountPercentToUse: 0.5 });
  }
}

// Save configuration and positions to file
export function saveConfig() {
  try {
    const config = {
      maxTradingBalance: maxTradingBalance,
      portfolio: currentStrategy.config,
      lastUpdated: new Date().toISOString()
    };

    // Save positions if using portfolio manager
    if (currentStrategy.constructor.name === 'PortfolioManager') {
      config.positions = savePositions();
      config.tradeHistory = saveTradeHistory();
    }

    fs.writeFileSync(CONFIG_FILE, JSON.stringify(config, null, 2));
  } catch (error) {
    log(`Failed to save config: ${error.message}`);
  }
}

// Save current positions to persistent storage
function savePositions() {
  try {
    if (currentStrategy.constructor.name === 'PortfolioManager') {
      const positions = {};
      const orders = {};

      // Save strategy positions
      currentStrategy.strategyPositions.forEach((position, strategyName) => {
        if (position.quantity > 0) {
          positions[strategyName] = {
            quantity: position.quantity,
            averagePrice: position.averagePrice,
            totalCost: position.totalCost,
            unrealizedPnL: position.unrealizedPnL,
            realizedPnL: position.realizedPnL,
            lastUpdateTime: position.lastUpdateTime
          };
        }
      });

      // Save active orders (stop losses, etc.)
      currentStrategy.strategyOrders.forEach((orderInfo, strategyName) => {
        if (orderInfo.activeStopLoss || orderInfo.pendingOrders.length > 0) {
          orders[strategyName] = {
            activeStopLoss: orderInfo.activeStopLoss,
            pendingOrders: orderInfo.pendingOrders,
            lastOrderTime: orderInfo.lastOrderTime
          };
        }
      });

      return { positions, orders, savedAt: Date.now() };
    }
    return null;
  } catch (error) {
    log(`Failed to save positions: ${error.message}`);
    return null;
  }
}

// Save trade history to persistent storage
function saveTradeHistory() {
  try {
    if (currentStrategy.constructor.name === 'PortfolioManager') {
      // Save last 100 trades to avoid file bloat
      const recentTrades = currentStrategy.tradeHistory.slice(-100);
      return {
        trades: recentTrades,
        savedAt: Date.now()
      };
    }
    return null;
  } catch (error) {
    log(`Failed to save trade history: ${error.message}`);
    return null;
  }
}

// Restore positions from persistent storage
function restorePositions(positionData) {
  try {
    if (currentStrategy.constructor.name !== 'PortfolioManager' || !positionData) {
      return;
    }

    const { positions, orders, savedAt } = positionData;
    const ageHours = (Date.now() - savedAt) / (1000 * 60 * 60);

    log(`🔄 Restoring positions saved ${ageHours.toFixed(1)} hours ago...`);

    // Restore strategy positions
    if (positions) {
      Object.entries(positions).forEach(([strategyName, position]) => {
        currentStrategy.strategyPositions.set(strategyName, {
          quantity: position.quantity,
          averagePrice: position.averagePrice,
          totalCost: position.totalCost,
          unrealizedPnL: position.unrealizedPnL,
          realizedPnL: position.realizedPnL,
          lastUpdateTime: position.lastUpdateTime
        });

        // Update strategy status to reflect position
        if (currentStrategy.strategyStatus.has(strategyName)) {
          const status = currentStrategy.strategyStatus.get(strategyName);
          currentStrategy.strategyStatus.set(strategyName, {
            ...status,
            hasPosition: position.quantity > 0,
            lastDecisionReason: `Position restored: ${position.quantity.toFixed(8)} BTC at avg $${position.averagePrice.toFixed(2)}`
          });
        }

        log(`  📊 ${strategyName}: ${position.quantity.toFixed(8)} BTC at avg $${position.averagePrice.toFixed(2)}`);
      });
    }

    // Restore active orders
    if (orders) {
      Object.entries(orders).forEach(([strategyName, orderInfo]) => {
        currentStrategy.strategyOrders.set(strategyName, {
          activeStopLoss: orderInfo.activeStopLoss,
          pendingOrders: orderInfo.pendingOrders || [],
          lastOrderTime: orderInfo.lastOrderTime
        });

        if (orderInfo.activeStopLoss) {
          log(`  🛡️ ${strategyName}: Active stop loss order ${orderInfo.activeStopLoss}`);
        }
      });
    }

    log(`✅ Position restoration complete`);
  } catch (error) {
    log(`❌ Failed to restore positions: ${error.message}`);
  }
}

// Restore trade history from persistent storage
function restoreTradeHistory(tradeData) {
  try {
    if (currentStrategy.constructor.name !== 'PortfolioManager' || !tradeData) {
      return;
    }

    const { trades, savedAt } = tradeData;
    const ageHours = (Date.now() - savedAt) / (1000 * 60 * 60);

    if (trades && trades.length > 0) {
      currentStrategy.tradeHistory = [...trades];
      log(`📈 Restored ${trades.length} trades from ${ageHours.toFixed(1)} hours ago`);
    }
  } catch (error) {
    log(`❌ Failed to restore trade history: ${error.message}`);
  }
}

// Validate positions against Robinhood API on startup
async function validatePositions() {
  try {
    if (currentStrategy.constructor.name !== 'PortfolioManager') {
      return;
    }

    log('🔍 Validating positions against Robinhood API...');

    // Get actual positions from Robinhood
    const positions = await RobinhoodAPI.getPositions();
    const btcPosition = positions.find(p => p.symbol === SYMBOL);
    const actualQuantity = parseFloat(btcPosition?.quantity || '0');

    // Calculate total tracked quantity across all strategies
    let totalTrackedQuantity = 0;
    currentStrategy.strategyPositions.forEach(position => {
      totalTrackedQuantity += position.quantity;
    });

    const difference = Math.abs(actualQuantity - totalTrackedQuantity);
    const tolerance = 0.00000001; // 1 satoshi tolerance

    if (difference > tolerance) {
      log(`⚠️ Position mismatch detected:`);
      log(`  Robinhood: ${actualQuantity.toFixed(8)} BTC`);
      log(`  Tracked: ${totalTrackedQuantity.toFixed(8)} BTC`);
      log(`  Difference: ${difference.toFixed(8)} BTC`);

      // If we have more tracked than actual, proportionally reduce all positions
      if (totalTrackedQuantity > actualQuantity) {
        const adjustmentRatio = actualQuantity / totalTrackedQuantity;
        log(`🔧 Adjusting tracked positions by ratio: ${adjustmentRatio.toFixed(6)}`);

        currentStrategy.strategyPositions.forEach((position, strategyName) => {
          if (position.quantity > 0) {
            const adjustedQuantity = position.quantity * adjustmentRatio;
            const adjustedCost = position.totalCost * adjustmentRatio;

            currentStrategy.strategyPositions.set(strategyName, {
              ...position,
              quantity: adjustedQuantity,
              totalCost: adjustedCost
            });

            log(`  📊 ${strategyName}: ${position.quantity.toFixed(8)} → ${adjustedQuantity.toFixed(8)} BTC`);
          }
        });
      }
      // If actual is more than tracked, log warning but don't auto-adjust
      else {
        log(`⚠️ Robinhood shows more BTC than tracked. Manual review recommended.`);
      }

      // Save corrected positions
      saveConfig();
    } else {
      log(`✅ Position validation passed: ${actualQuantity.toFixed(8)} BTC`);
    }

  } catch (error) {
    log(`❌ Failed to validate positions: ${error.message}`);
  }
}

// Helper function to get strategy name from class
function getStrategyName(strategy) {
  if (strategy.constructor.name === 'PortfolioManager') {
    return 'portfolio-manager';
  }
  
  // Convert class name to kebab-case
  return strategy.constructor.name
    .replace('Strategy', '')
    .replace(/([A-Z])/g, (match, letter, index) => {
      return index === 0 ? letter.toLowerCase() : '-' + letter.toLowerCase();
    });
}

// Process exit handlers to save data
process.on('SIGINT', async () => {
  console.log('\n🛑 Received SIGINT, saving data and shutting down...');
  try {
    candleService.forceSave();
    saveConfig();
    console.log('💾 Data saved successfully');
  } catch (error) {
    console.error('❌ Failed to save data on exit:', error);
  }
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Received SIGTERM, saving data and shutting down...');
  try {
    candleService.forceSave();
    saveConfig();
    console.log('💾 Data saved successfully');
  } catch (error) {
    console.error('❌ Failed to save data on exit:', error);
  }
  process.exit(0);
});

// Load config on module initialization
loadConfig().catch(error => {
  console.error('Failed to load config on startup:', error.message);
});
