import { getCurrentStrategy } from './bot.js';

async function debugAllocations() {
  try {
    const strategy = getCurrentStrategy();
    console.log('Strategy type:', strategy.constructor.name);
    
    if (strategy.constructor.name === 'PortfolioManager') {
      console.log('\n=== Strategy Allocations ===');
      strategy.strategyAllocations.forEach((allocation, name) => {
        console.log(`  ${name}: ${allocation} (${(allocation * 100).toFixed(1)}%)`);
      });
      
      console.log('\n=== Strategy Config ===');
      console.log('Config strategies:', strategy.config.strategies.map(s => ({
        name: s.name,
        allocation: s.allocation,
        enabled: s.enabled !== false
      })));
      
      console.log('\n=== Stats Output ===');
      const stats = strategy.getStats();
      Object.entries(stats.strategies).forEach(([name, strategyStats]) => {
        console.log(`${name}: allocation = "${strategyStats.allocation}"`);
      });
    }
  } catch (error) {
    console.error('Error:', error);
  }
}

debugAllocations();
