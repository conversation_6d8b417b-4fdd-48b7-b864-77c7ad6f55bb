import { createStrategy } from './index.js';
import Big from 'big.js';

export class PortfolioManager {
  constructor(config = {}) {
    this.config = {
      strategies: [], // Array of {name, config, allocation}
      rebalanceFrequency: 24 * 60 * 60 * 1000, // 24 hours in ms
      performanceWindow: 7 * 24 * 60 * 60 * 1000, // 7 days for performance calc
      minAllocation: 0.05, // Minimum 5% allocation per strategy
      maxAllocation: 0.6, // Maximum 60% allocation per strategy
      rebalanceThreshold: 0.1, // Rebalance when performance diff > 10%
      ...config
    };
    
    this.strategyInstances = new Map();
    this.strategyPerformance = new Map();
    this.strategyAllocations = new Map();
    this.strategyStatus = new Map(); // Track current status of each strategy
    this.strategyPositions = new Map(); // Track individual strategy positions
    this.strategyOrders = new Map(); // Track active orders per strategy
    this.lastRebalance = Date.now();
    this.tradeHistory = [];
    
    this.initializeStrategies();
  }

  initializeStrategies() {
    this.config.strategies.forEach(strategyConfig => {
      const strategy = createStrategy(strategyConfig.name, strategyConfig.config);
      this.strategyInstances.set(strategyConfig.name, strategy);
      this.strategyAllocations.set(strategyConfig.name, strategyConfig.allocation || 1 / this.config.strategies.length);
      this.strategyPerformance.set(strategyConfig.name, {
        trades: [],
        totalReturn: 0,
        winRate: 0,
        sharpeRatio: 0,
        lastUpdate: Date.now()
      });
      this.strategyStatus.set(strategyConfig.name, {
        lastDecision: null,
        lastDecisionTime: null,
        lastDecisionReason: 'Strategy initialized, waiting for first price update',
        nextSignal: 'Waiting for market data',
        hasPosition: false,
        lastTradeTime: null,
        lastTradeType: null,
        lastTradePrice: null
      });

      // Initialize position tracking for each strategy
      this.strategyPositions.set(strategyConfig.name, {
        quantity: 0,
        averagePrice: 0,
        totalCost: 0,
        unrealizedPnL: 0,
        realizedPnL: 0,
        lastUpdateTime: Date.now()
      });

      // Initialize order tracking for each strategy
      this.strategyOrders.set(strategyConfig.name, {
        activeStopLoss: null,
        pendingOrders: [],
        lastOrderTime: null
      });
    });
  }

  updatePrice(price) {
    // Update all strategies with current price
    this.strategyInstances.forEach(strategy => {
      strategy.updatePrice(price);
    });
    
    // Check if it's time to rebalance
    if (Date.now() - this.lastRebalance > this.config.rebalanceFrequency) {
      this.rebalanceAllocations();
    }
  }

  shouldEnterTrade(currentPrice, marketData) {
    const decisions = [];
    const totalBalance = parseFloat(marketData.availableBalance || 0);
    const currentTime = Date.now();

    // Update unrealized P&L for all positions
    this.updateUnrealizedPnL(currentPrice);

    this.strategyInstances.forEach((strategy, name) => {
      const allocation = this.strategyAllocations.get(name);
      const strategyBalance = totalBalance * allocation;
      const currentPosition = this.getStrategyPosition(name);

      if (strategyBalance < 10) {
        // Update status for insufficient balance
        this.strategyStatus.set(name, {
          ...this.strategyStatus.get(name),
          lastDecision: false,
          lastDecisionTime: currentTime,
          lastDecisionReason: `Insufficient allocation: $${strategyBalance.toFixed(2)} (minimum $10 required)`,
          nextSignal: 'Waiting for larger allocation or rebalancing'
        });
        return;
      }

      // Calculate available balance for this strategy (allocation minus current position value)
      const availableForStrategy = Math.max(0, strategyBalance - currentPosition.totalCost);

      const decision = strategy.shouldEnterTrade(currentPrice, {
        ...marketData,
        availableBalance: availableForStrategy,
        currentPosition: currentPosition,
        hasPosition: currentPosition.quantity > 0
      });

      // Update strategy status with decision information
      const status = this.strategyStatus.get(name) || {};
      this.strategyStatus.set(name, {
        ...status,
        lastDecision: decision.shouldEnter,
        lastDecisionTime: currentTime,
        lastDecisionReason: decision.reason || 'No reason provided',
        nextSignal: this.getNextSignalDescription(strategy, name, currentPrice, decision)
      });

      if (decision.shouldEnter && availableForStrategy >= 10) {
        decisions.push({
          strategy: name,
          decision,
          allocation,
          balance: availableForStrategy,
          currentPosition
        });
      }
    });
    
    // Return the decision from the strategy with highest allocation that wants to trade
    if (decisions.length === 0) {
      return { shouldEnter: false, reason: 'No strategies want to enter' };
    }
    
    // Sort by allocation (highest first) and return best decision
    decisions.sort((a, b) => b.allocation - a.allocation);
    const bestDecision = decisions[0];
    
    return {
      shouldEnter: true,
      reason: `${bestDecision.strategy}: ${bestDecision.decision.reason} (${(bestDecision.allocation * 100).toFixed(1)}% allocation)`,
      strategy: bestDecision.strategy,
      allocation: bestDecision.allocation
    };
  }

  calculateStopLoss(currentPrice, marketData) {
    // Use the strategy that made the last trade
    const lastTrade = this.getLastTrade();
    if (lastTrade && lastTrade.strategy) {
      const strategy = this.strategyInstances.get(lastTrade.strategy);
      if (strategy) {
        const stopLoss = strategy.calculateStopLoss(currentPrice, marketData);
        return {
          ...stopLoss,
          reason: `${lastTrade.strategy}: ${stopLoss.reason}`
        };
      }
    }
    
    // Fallback to first strategy
    const firstStrategy = this.strategyInstances.values().next().value;
    return firstStrategy.calculateStopLoss(currentPrice, marketData);
  }

  getPositionSize(availableCash, currentPrice, strategyName = null) {
    if (strategyName) {
      const strategy = this.strategyInstances.get(strategyName);
      const allocation = this.strategyAllocations.get(strategyName);
      const strategyBalance = availableCash * allocation;
      
      if (strategy) {
        const positionSize = strategy.getPositionSize(strategyBalance, currentPrice);
        return {
          ...positionSize,
          reason: `${strategyName} (${(allocation * 100).toFixed(1)}% allocation): ${positionSize.reason}`
        };
      }
    }
    
    // Fallback
    const firstStrategy = this.strategyInstances.values().next().value;
    return firstStrategy.getPositionSize(availableCash, currentPrice);
  }

  recordTrade(type, price, quantity, strategyName) {
    const trade = {
      type,
      price,
      quantity,
      strategy: strategyName,
      timestamp: Date.now(),
      value: price * quantity
    };

    this.tradeHistory.push(trade);

    // Update strategy position tracking
    this.updateStrategyPosition(strategyName, type, price, quantity);

    // Update strategy status with trade information
    if (this.strategyStatus.has(strategyName)) {
      const status = this.strategyStatus.get(strategyName);
      const position = this.strategyPositions.get(strategyName);
      this.strategyStatus.set(strategyName, {
        ...status,
        hasPosition: position ? position.quantity > 0 : false,
        lastTradeTime: Date.now(),
        lastTradeType: type,
        lastTradePrice: price,
        lastDecisionReason: `${type.toUpperCase()} executed at $${price.toFixed(2)}`,
        nextSignal: type === 'buy' ? 'Monitoring for exit signals' : 'Waiting for next entry opportunity'
      });
    }

    // Update strategy performance
    if (type === 'sell') {
      this.updateStrategyPerformance(strategyName, trade);
    }
  }

  // Update individual strategy position tracking
  updateStrategyPosition(strategyName, type, price, quantity) {
    const position = this.strategyPositions.get(strategyName);
    if (!position) return;

    if (type === 'buy') {
      // Add to position using weighted average cost
      const newTotalCost = position.totalCost + (price * quantity);
      const newQuantity = position.quantity + quantity;
      const newAveragePrice = newQuantity > 0 ? newTotalCost / newQuantity : 0;

      this.strategyPositions.set(strategyName, {
        ...position,
        quantity: newQuantity,
        averagePrice: newAveragePrice,
        totalCost: newTotalCost,
        lastUpdateTime: Date.now()
      });
    } else if (type === 'sell') {
      // Reduce position and calculate realized P&L
      const soldCost = position.averagePrice * quantity;
      const realizedPnL = (price - position.averagePrice) * quantity;

      this.strategyPositions.set(strategyName, {
        ...position,
        quantity: Math.max(0, position.quantity - quantity),
        totalCost: Math.max(0, position.totalCost - soldCost),
        realizedPnL: position.realizedPnL + realizedPnL,
        lastUpdateTime: Date.now()
      });
    }
  }

  updateStrategyPerformance(strategyName, sellTrade) {
    const performance = this.strategyPerformance.get(strategyName);
    if (!performance) return;
    
    // Find corresponding buy trade
    const buyTrade = this.tradeHistory
      .slice()
      .reverse()
      .find(t => t.type === 'buy' && t.strategy === strategyName);
    
    if (buyTrade) {
      const returnPct = (sellTrade.price - buyTrade.price) / buyTrade.price;
      const profit = (sellTrade.price - buyTrade.price) * sellTrade.quantity;
      
      performance.trades.push({
        buyPrice: buyTrade.price,
        sellPrice: sellTrade.price,
        return: returnPct,
        profit,
        duration: sellTrade.timestamp - buyTrade.timestamp
      });
      
      // Calculate updated metrics
      this.calculatePerformanceMetrics(strategyName);
    }
  }

  calculatePerformanceMetrics(strategyName) {
    const performance = this.strategyPerformance.get(strategyName);
    const trades = performance.trades;
    
    if (trades.length === 0) return;
    
    // Total return
    performance.totalReturn = trades.reduce((sum, trade) => sum + trade.return, 0);
    
    // Win rate
    const wins = trades.filter(trade => trade.return > 0).length;
    performance.winRate = wins / trades.length;
    
    // Simple Sharpe ratio approximation
    const returns = trades.map(trade => trade.return);
    const avgReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - avgReturn, 2), 0) / returns.length;
    const stdDev = Math.sqrt(variance);
    performance.sharpeRatio = stdDev > 0 ? avgReturn / stdDev : 0;
    
    performance.lastUpdate = Date.now();
  }

  rebalanceAllocations() {
    console.log('Rebalancing strategy allocations...');
    
    const performances = Array.from(this.strategyPerformance.entries())
      .map(([name, perf]) => ({
        name,
        score: this.calculateStrategyScore(perf),
        currentAllocation: this.strategyAllocations.get(name)
      }))
      .sort((a, b) => b.score - a.score);
    
    // Redistribute allocations based on performance
    const totalScore = performances.reduce((sum, p) => sum + Math.max(p.score, 0.1), 0);
    
    performances.forEach(perf => {
      const baseScore = Math.max(perf.score, 0.1);
      let newAllocation = baseScore / totalScore;
      
      // Apply min/max constraints
      newAllocation = Math.max(this.config.minAllocation, newAllocation);
      newAllocation = Math.min(this.config.maxAllocation, newAllocation);
      
      this.strategyAllocations.set(perf.name, newAllocation);
    });
    
    // Normalize allocations to sum to 1
    this.normalizeAllocations();
    
    this.lastRebalance = Date.now();
    
    // Log new allocations
    this.logAllocations();
  }

  calculateStrategyScore(performance) {
    if (performance.trades.length === 0) return 1; // Default score for new strategies
    
    // Weighted score based on multiple factors
    const returnWeight = 0.4;
    const winRateWeight = 0.3;
    const sharpeWeight = 0.3;
    
    const normalizedReturn = Math.max(-1, Math.min(2, performance.totalReturn)); // Cap between -100% and 200%
    const normalizedSharpe = Math.max(-2, Math.min(3, performance.sharpeRatio)); // Cap Sharpe ratio
    
    return (normalizedReturn * returnWeight) + 
           (performance.winRate * winRateWeight) + 
           (normalizedSharpe * sharpeWeight);
  }

  normalizeAllocations() {
    const total = Array.from(this.strategyAllocations.values()).reduce((sum, alloc) => sum + alloc, 0);
    
    if (total > 0) {
      this.strategyAllocations.forEach((allocation, name) => {
        this.strategyAllocations.set(name, allocation / total);
      });
    }
  }

  logAllocations() {
    console.log('Strategy Allocations:');
    this.strategyAllocations.forEach((allocation, name) => {
      const performance = this.strategyPerformance.get(name);
      console.log(`  ${name}: ${(allocation * 100).toFixed(1)}% (Return: ${(performance.totalReturn * 100).toFixed(1)}%, Win Rate: ${(performance.winRate * 100).toFixed(1)}%)`);
    });
  }

  getNextSignalDescription(strategy, strategyName, currentPrice, decision) {
    // Generate a description of what the strategy is waiting for
    if (decision.shouldEnter) {
      return 'Ready to enter trade';
    }

    // Strategy-specific next signal descriptions with detailed conditions
    switch (strategyName) {
      case 'trend-following':
        if (strategy.priceHistory.length < strategy.config.minTrendLookback) {
          return `Accumulating price data (${strategy.priceHistory.length}/${strategy.config.minTrendLookback} required)`;
        }

        // Check specific trend conditions
        const isUptrend = strategy.isUptrend(strategy.priceHistory, strategy.config.minTrendLookback);
        const shortSMA = strategy.calculateSMA(strategy.priceHistory, strategy.config.shortTermPeriod || 5);
        const recentHigh = Math.max(...strategy.priceHistory.slice(-10));
        const highThreshold = recentHigh * (strategy.config.recentHighThreshold || 0.98);

        if (!isUptrend) {
          const longSMA = strategy.calculateSMA(strategy.priceHistory, strategy.config.minTrendLookback);
          return `Waiting for uptrend: price $${currentPrice.toFixed(2)} needs to be above ${strategy.config.minTrendLookback}-period SMA $${longSMA.toFixed(2)}`;
        }

        if (currentPrice < shortSMA) {
          return `Waiting for pullback to end: price $${currentPrice.toFixed(2)} below short-term SMA $${shortSMA.toFixed(2)}`;
        }

        if (currentPrice > highThreshold) {
          return `Waiting for better entry: price $${currentPrice.toFixed(2)} too close to recent high $${recentHigh.toFixed(2)} (threshold: $${highThreshold.toFixed(2)})`;
        }

        return `Monitoring trend conditions: price $${currentPrice.toFixed(2)}`;

      case 'mean-reversion':
        if (strategy.priceHistory.length < (strategy.config.meanPeriod || strategy.config.lookbackPeriod || 20)) {
          const required = strategy.config.meanPeriod || strategy.config.lookbackPeriod || 20;
          return `Building price history (${strategy.priceHistory.length}/${required} required)`;
        }

        const meanPeriod = strategy.config.meanPeriod || strategy.config.lookbackPeriod || 20;
        const sma = strategy.calculateSMA(strategy.priceHistory, meanPeriod);
        const deviationThreshold = strategy.config.deviationThreshold || 2.0;

        if (sma) {
          const deviation = ((sma - currentPrice) / sma * 100);
          const targetDeviation = deviationThreshold * 100;
          const targetPrice = sma * (1 - deviationThreshold / 100);

          if (deviation >= 0) {
            return `Waiting for oversold condition: price $${currentPrice.toFixed(2)} needs to drop ${targetDeviation.toFixed(1)}% below mean $${sma.toFixed(2)} (target: $${targetPrice.toFixed(2)})`;
          } else {
            return `Price above mean: $${currentPrice.toFixed(2)} vs mean $${sma.toFixed(2)} (${Math.abs(deviation).toFixed(1)}% above)`;
          }
        }
        return `Calculating mean from ${meanPeriod} periods`;

      case 'breakout':
        if (strategy.priceHistory.length < (strategy.config.breakoutLookback || 20)) {
          const required = strategy.config.breakoutLookback || 20;
          return `Accumulating price data (${strategy.priceHistory.length}/${required} required)`;
        }

        const lookback = strategy.config.breakoutLookback || 20;
        const breakoutHigh = Math.max(...strategy.priceHistory.slice(-lookback));
        const breakoutThreshold = breakoutHigh * 1.01; // 1% above recent high
        const distanceToBreakout = ((breakoutThreshold - currentPrice) / currentPrice * 100);

        return `Waiting for breakout: price $${currentPrice.toFixed(2)} needs to break above $${breakoutThreshold.toFixed(2)} (${distanceToBreakout.toFixed(1)}% to go)`;

      case 'moving-average-crossover':
        if (strategy.priceHistory.length < Math.max(strategy.config.shortPeriod || 5, strategy.config.longPeriod || 20)) {
          const required = Math.max(strategy.config.shortPeriod || 5, strategy.config.longPeriod || 20);
          return `Building price history (${strategy.priceHistory.length}/${required} required)`;
        }

        const shortMA = strategy.calculateSMA(strategy.priceHistory, strategy.config.shortPeriod || 5);
        const longMA = strategy.calculateSMA(strategy.priceHistory, strategy.config.longPeriod || 20);

        if (shortMA && longMA) {
          const crossoverDistance = ((shortMA - longMA) / longMA * 100);
          if (shortMA <= longMA) {
            return `Waiting for golden cross: ${strategy.config.shortPeriod || 5}-MA $${shortMA.toFixed(2)} needs to cross above ${strategy.config.longPeriod || 20}-MA $${longMA.toFixed(2)} (${Math.abs(crossoverDistance).toFixed(1)}% below)`;
          } else {
            return `Golden cross active: ${strategy.config.shortPeriod || 5}-MA $${shortMA.toFixed(2)} above ${strategy.config.longPeriod || 20}-MA $${longMA.toFixed(2)} (${crossoverDistance.toFixed(1)}% above)`;
          }
        }
        return 'Calculating moving averages';

      case 'range-trading':
        if (strategy.priceHistory.length < (strategy.config.rangeLookback || 20)) {
          const required = strategy.config.rangeLookback || 20;
          return `Accumulating price data (${strategy.priceHistory.length}/${required} required)`;
        }

        const rangeLookback = strategy.config.rangeLookback || 20;
        const recentPrices = strategy.priceHistory.slice(-rangeLookback);
        const rangeHigh = Math.max(...recentPrices);
        const rangeLow = Math.min(...recentPrices);
        const rangeSize = rangeHigh - rangeLow;
        const supportLevel = rangeLow + (rangeSize * 0.1); // 10% above range low

        return `Monitoring range: support $${supportLevel.toFixed(2)}, resistance $${rangeHigh.toFixed(2)}, current $${currentPrice.toFixed(2)}`;

      case 'simple':
        return `Simple strategy: ready to buy at current price $${currentPrice.toFixed(2)}`;

      default:
        return `Monitoring market conditions at $${currentPrice.toFixed(2)}`;
    }
  }

  getLastTrade() {
    return this.tradeHistory[this.tradeHistory.length - 1];
  }

  // Get position information for a specific strategy
  getStrategyPosition(strategyName) {
    return this.strategyPositions.get(strategyName) || {
      quantity: 0,
      averagePrice: 0,
      totalCost: 0,
      unrealizedPnL: 0,
      realizedPnL: 0,
      lastUpdateTime: Date.now()
    };
  }

  // Get total portfolio position (sum of all strategies)
  getTotalPosition() {
    let totalQuantity = 0;
    let totalCost = 0;
    let totalRealizedPnL = 0;

    this.strategyPositions.forEach(position => {
      totalQuantity += position.quantity;
      totalCost += position.totalCost;
      totalRealizedPnL += position.realizedPnL;
    });

    const averagePrice = totalQuantity > 0 ? totalCost / totalQuantity : 0;
    return {
      quantity: totalQuantity,
      averagePrice,
      totalCost,
      realizedPnL: totalRealizedPnL
    };
  }

  // Update unrealized P&L for all strategies
  updateUnrealizedPnL(currentPrice) {
    this.strategyPositions.forEach((position, strategyName) => {
      if (position.quantity > 0) {
        const unrealizedPnL = (currentPrice - position.averagePrice) * position.quantity;
        this.strategyPositions.set(strategyName, {
          ...position,
          unrealizedPnL,
          lastUpdateTime: Date.now()
        });
      }
    });
  }

  // Check if a strategy can enter a new position based on current allocations
  canStrategyTrade(strategyName, requestedAmount, totalAvailableBalance) {
    const allocation = this.strategyAllocations.get(strategyName) || 0;
    const maxAllowedBalance = totalAvailableBalance * allocation;
    const currentPosition = this.getStrategyPosition(strategyName);
    const currentValue = currentPosition.totalCost;

    return (currentValue + requestedAmount) <= maxAllowedBalance;
  }

  // Handle max trading balance changes by reconciling positions
  reconcilePositionsForBalanceChange(newMaxBalance, currentPrice) {
    const totalPosition = this.getTotalPosition();
    const currentTotalValue = totalPosition.quantity * currentPrice;

    if (currentTotalValue <= newMaxBalance) {
      // No action needed, current positions fit within new limit
      return { action: 'none', message: 'Current positions fit within new balance limit' };
    }

    // Calculate how much we need to reduce
    const excessValue = currentTotalValue - newMaxBalance;
    const reductionRatio = excessValue / currentTotalValue;

    const reductionPlan = [];

    // Calculate proportional reduction for each strategy
    this.strategyPositions.forEach((position, strategyName) => {
      if (position.quantity > 0) {
        const positionValue = position.quantity * currentPrice;
        const reductionAmount = positionValue * reductionRatio;
        const quantityToSell = reductionAmount / currentPrice;

        if (quantityToSell > 0.00001) { // Minimum trade size
          reductionPlan.push({
            strategy: strategyName,
            currentQuantity: position.quantity,
            quantityToSell: quantityToSell,
            valueToSell: reductionAmount,
            reason: `Reducing position due to max balance limit change`
          });
        }
      }
    });

    return {
      action: 'reduce',
      message: `Need to reduce positions by $${excessValue.toFixed(2)} to comply with new limit`,
      reductionPlan,
      totalReduction: excessValue
    };
  }

  // Get strategies that need to exit positions (for stop losses or rebalancing)
  getStrategiesNeedingExit(currentPrice) {
    const exitDecisions = [];

    this.strategyInstances.forEach((strategy, name) => {
      const position = this.getStrategyPosition(name);

      if (position.quantity > 0) {
        // Check if strategy wants to exit
        const exitDecision = strategy.shouldExitTrade ?
          strategy.shouldExitTrade(currentPrice, { currentPosition: position }) :
          { shouldExit: false, reason: 'No exit logic implemented' };

        if (exitDecision.shouldExit) {
          exitDecisions.push({
            strategy: name,
            decision: exitDecision,
            position,
            quantityToSell: exitDecision.quantity || position.quantity
          });
        }
      }
    });

    return exitDecisions;
  }

  // Manage stop loss orders for individual strategies
  updateStrategyStopLoss(strategyName, orderId, stopPrice) {
    const orders = this.strategyOrders.get(strategyName);
    if (orders) {
      orders.activeStopLoss = {
        orderId,
        stopPrice,
        timestamp: Date.now()
      };
      orders.lastOrderTime = Date.now();
      this.strategyOrders.set(strategyName, orders);
    }
  }

  // Cancel stop loss for a specific strategy
  cancelStrategyStopLoss(strategyName) {
    const orders = this.strategyOrders.get(strategyName);
    if (orders && orders.activeStopLoss) {
      const orderId = orders.activeStopLoss.orderId;
      orders.activeStopLoss = null;
      this.strategyOrders.set(strategyName, orders);
      return orderId;
    }
    return null;
  }

  // Get all active stop loss orders
  getActiveStopLossOrders() {
    const activeOrders = [];
    this.strategyOrders.forEach((orders, strategyName) => {
      if (orders.activeStopLoss) {
        activeOrders.push({
          strategy: strategyName,
          orderId: orders.activeStopLoss.orderId,
          stopPrice: orders.activeStopLoss.stopPrice,
          timestamp: orders.activeStopLoss.timestamp
        });
      }
    });
    return activeOrders;
  }

  // Calculate stop loss for a specific strategy
  calculateStrategyStopLoss(strategyName, currentPrice, marketData) {
    const strategy = this.strategyInstances.get(strategyName);
    const position = this.getStrategyPosition(strategyName);

    if (!strategy || position.quantity === 0) {
      return null;
    }

    const stopLossDecision = strategy.calculateStopLoss(currentPrice, {
      ...marketData,
      currentPosition: position
    });

    return {
      ...stopLossDecision,
      strategy: strategyName,
      quantity: position.quantity,
      reason: `${strategyName}: ${stopLossDecision.reason}`
    };
  }

  // Safe strategy disable with position handling
  canDisableStrategy(strategyName) {
    const position = this.getStrategyPosition(strategyName);
    const orders = this.strategyOrders.get(strategyName);

    return {
      canDisable: position.quantity === 0 && (!orders || !orders.activeStopLoss),
      hasPosition: position.quantity > 0,
      positionValue: position.quantity * (position.averagePrice || 0),
      hasActiveOrders: orders && orders.activeStopLoss !== null,
      position: position,
      orders: orders
    };
  }

  // Prepare strategy for safe disable (close positions, cancel orders)
  async prepareStrategyForDisable(strategyName, currentPrice, forceClose = false) {
    const position = this.getStrategyPosition(strategyName);
    const orders = this.strategyOrders.get(strategyName);
    const actions = [];

    // Cancel any active stop loss orders
    if (orders && orders.activeStopLoss) {
      actions.push({
        type: 'cancel_order',
        orderId: orders.activeStopLoss.orderId,
        reason: `Canceling stop loss for strategy disable: ${strategyName}`
      });
    }

    // Handle open position
    if (position.quantity > 0) {
      if (forceClose) {
        actions.push({
          type: 'market_sell',
          strategy: strategyName,
          quantity: position.quantity,
          estimatedValue: position.quantity * currentPrice,
          reason: `Force closing position for strategy disable: ${strategyName}`
        });
      } else {
        actions.push({
          type: 'warning',
          message: `Strategy ${strategyName} has open position: ${position.quantity.toFixed(8)} BTC (value: $${(position.quantity * currentPrice).toFixed(2)})`
        });
      }
    }

    return {
      strategyName,
      canProceed: forceClose || position.quantity === 0,
      actions,
      position,
      orders
    };
  }

  // Execute strategy disable with proper cleanup
  disableStrategy(strategyName, options = {}) {
    const {
      redistributeAllocation = true,
      cleanupPosition = false,
      cleanupOrders = false
    } = options;

    // Get current allocation before removal
    const removedAllocation = this.strategyAllocations.get(strategyName) || 0;

    // Remove strategy from all tracking maps
    this.strategyInstances.delete(strategyName);
    this.strategyAllocations.delete(strategyName);
    this.strategyPerformance.delete(strategyName);
    this.strategyStatus.delete(strategyName);

    // Clean up position and order data if requested
    if (cleanupPosition) {
      this.strategyPositions.delete(strategyName);
    }

    if (cleanupOrders) {
      this.strategyOrders.delete(strategyName);
    }

    // Redistribute allocation to remaining strategies
    if (redistributeAllocation && removedAllocation > 0) {
      const remainingStrategies = Array.from(this.strategyAllocations.keys());
      if (remainingStrategies.length > 0) {
        const redistributionAmount = removedAllocation / remainingStrategies.length;
        remainingStrategies.forEach(name => {
          const currentAllocation = this.strategyAllocations.get(name);
          this.strategyAllocations.set(name, currentAllocation + redistributionAmount);
        });
      }
    }

    // Update config to reflect the change
    this.config.strategies = this.config.strategies.filter(s => s.name !== strategyName);

    return {
      success: true,
      removedAllocation,
      redistributedTo: Array.from(this.strategyAllocations.keys()),
      cleanedUpPosition: cleanupPosition,
      cleanedUpOrders: cleanupOrders
    };
  }

  // Get orphaned positions (positions for disabled strategies)
  getOrphanedPositions() {
    const orphaned = [];

    this.strategyPositions.forEach((position, strategyName) => {
      if (!this.strategyInstances.has(strategyName) && position.quantity > 0) {
        orphaned.push({
          strategy: strategyName,
          position,
          isOrphaned: true
        });
      }
    });

    return orphaned;
  }

  // Clean up all orphaned data
  cleanupOrphanedData() {
    const activeStrategies = new Set(this.strategyInstances.keys());
    const cleanedUp = {
      positions: [],
      orders: [],
      performance: [],
      status: []
    };

    // Clean up positions
    this.strategyPositions.forEach((position, name) => {
      if (!activeStrategies.has(name)) {
        cleanedUp.positions.push({ strategy: name, position });
        this.strategyPositions.delete(name);
      }
    });

    // Clean up orders
    this.strategyOrders.forEach((orders, name) => {
      if (!activeStrategies.has(name)) {
        cleanedUp.orders.push({ strategy: name, orders });
        this.strategyOrders.delete(name);
      }
    });

    // Clean up performance data
    this.strategyPerformance.forEach((performance, name) => {
      if (!activeStrategies.has(name)) {
        cleanedUp.performance.push({ strategy: name, performance });
        this.strategyPerformance.delete(name);
      }
    });

    // Clean up status data
    this.strategyStatus.forEach((status, name) => {
      if (!activeStrategies.has(name)) {
        cleanedUp.status.push({ strategy: name, status });
        this.strategyStatus.delete(name);
      }
    });

    return cleanedUp;
  }

  getName() {
    return 'Portfolio Manager';
  }

  getDescription() {
    return `Multi-strategy portfolio manager running ${this.strategyInstances.size} strategies with dynamic allocation based on performance.`;
  }

  getStats() {
    const stats = {
      strategies: {},
      totalTrades: this.tradeHistory.length,
      rebalanceFrequency: this.config.rebalanceFrequency / (60 * 60 * 1000) + ' hours',
      lastRebalance: new Date(this.lastRebalance).toISOString()
    };
    
    this.strategyInstances.forEach((strategyInstance, name) => {
      const allocation = this.strategyAllocations.get(name);
      const performance = this.strategyPerformance.get(name);
      const status = this.strategyStatus.get(name);
      const position = this.getStrategyPosition(name);

      stats.strategies[name] = {
        allocation: (allocation * 100).toFixed(1) + '%',
        trades: performance.trades.length,
        totalReturn: (performance.totalReturn * 100).toFixed(2) + '%',
        winRate: (performance.winRate * 100).toFixed(1) + '%',
        sharpeRatio: performance.sharpeRatio.toFixed(2),
        position: {
          quantity: position.quantity.toFixed(8),
          averagePrice: position.averagePrice.toFixed(2),
          totalCost: position.totalCost.toFixed(2),
          unrealizedPnL: position.unrealizedPnL.toFixed(2),
          realizedPnL: position.realizedPnL.toFixed(2),
          hasPosition: position.quantity > 0
        },
        status: {
          lastDecision: status?.lastDecision,
          lastDecisionTime: status?.lastDecisionTime,
          lastDecisionReason: status?.lastDecisionReason || 'No recent decisions',
          nextSignal: status?.nextSignal || 'Monitoring market',
          hasPosition: position.quantity > 0,
          lastTradeTime: status?.lastTradeTime,
          lastTradeType: status?.lastTradeType,
          lastTradePrice: status?.lastTradePrice
        }
      };
    });
    
    return stats;
  }
}