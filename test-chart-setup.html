<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Chart Setup Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      background: #1f2937;
      color: #ffffff;
    }
    .chart-container {
      height: 400px;
      margin: 20px 0;
      background: #374151;
      border-radius: 8px;
      padding: 20px;
    }
    .test-section {
      margin: 20px 0;
      padding: 20px;
      border: 1px solid #374151;
      border-radius: 8px;
    }
    button {
      margin: 5px;
      padding: 8px 16px;
      background: #3b82f6;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    button:hover {
      background: #2563eb;
    }
    pre {
      background: #111827;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
      font-size: 12px;
    }
  </style>
</head>
<body>
  <h1>Chart Setup Test</h1>
  
  <div class="test-section">
    <h2>1. Chart.js Library Test</h2>
    <button onclick="testChartJS()">Test Chart.js</button>
    <button onclick="testCandlestickPlugin()">Test Candlestick Plugin</button>
    <div id="libraryResults"></div>
  </div>

  <div class="test-section">
    <h2>2. API Endpoint Test</h2>
    <button onclick="testCandleAPI()">Test Candle API</button>
    <button onclick="testWithMockData()">Test with Mock Data</button>
    <div id="apiResults"></div>
  </div>

  <div class="test-section">
    <h2>3. Candlestick Chart Test</h2>
    <button onclick="createTestChart()">Create Test Chart</button>
    <button onclick="addMockCandles()">Add Mock Candles</button>
    <div class="chart-container">
      <canvas id="testChart"></canvas>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chartjs-chart-financial/dist/chartjs-chart-financial.min.js"></script>

  <script>
    let testChart;

    function testChartJS() {
      const results = document.getElementById('libraryResults');
      
      if (typeof Chart !== 'undefined') {
        results.innerHTML = `
          <div style="color: #10b981;">✅ Chart.js loaded successfully</div>
          <div>Version: ${Chart.version || 'Unknown'}</div>
        `;
      } else {
        results.innerHTML = `<div style="color: #ef4444;">❌ Chart.js not loaded</div>`;
      }
    }

    function testCandlestickPlugin() {
      const results = document.getElementById('libraryResults');
      
      if (typeof Chart !== 'undefined' && Chart.registry.getController('candlestick')) {
        results.innerHTML += `<div style="color: #10b981;">✅ Candlestick plugin loaded</div>`;
      } else {
        results.innerHTML += `<div style="color: #ef4444;">❌ Candlestick plugin not loaded</div>`;
      }
    }

    async function testCandleAPI() {
      const results = document.getElementById('apiResults');
      
      try {
        const response = await fetch('/api/candles/5m?period=1d');
        if (response.ok) {
          const data = await response.json();
          results.innerHTML = `
            <div style="color: #10b981;">✅ API endpoint working</div>
            <pre>${JSON.stringify(data, null, 2)}</pre>
          `;
        } else {
          results.innerHTML = `<div style="color: #ef4444;">❌ API failed: ${response.status}</div>`;
        }
      } catch (error) {
        results.innerHTML = `<div style="color: #ef4444;">❌ API error: ${error.message}</div>`;
      }
    }

    function testWithMockData() {
      const results = document.getElementById('apiResults');
      
      // Generate mock candle data
      const now = Date.now();
      const mockCandles = [];
      
      for (let i = 0; i < 20; i++) {
        const time = now - (20 - i) * 5 * 60 * 1000; // 5-minute intervals
        const basePrice = 119000 + (Math.random() - 0.5) * 2000;
        const open = basePrice;
        const close = basePrice + (Math.random() - 0.5) * 500;
        const high = Math.max(open, close) + Math.random() * 200;
        const low = Math.min(open, close) - Math.random() * 200;
        
        mockCandles.push({
          t: time,
          o: open,
          h: high,
          l: low,
          c: close,
          v: Math.floor(Math.random() * 100)
        });
      }
      
      results.innerHTML = `
        <div style="color: #10b981;">✅ Mock data generated</div>
        <div>Generated ${mockCandles.length} candles</div>
        <pre>${JSON.stringify(mockCandles.slice(0, 3), null, 2)}...</pre>
      `;
      
      // Store for chart test
      window.mockCandles = mockCandles;
    }

    function createTestChart() {
      const ctx = document.getElementById('testChart').getContext('2d');
      
      if (testChart) {
        testChart.destroy();
      }
      
      try {
        testChart = new Chart(ctx, {
          type: 'candlestick',
          data: {
            datasets: [{
              label: 'Test Candlesticks',
              data: [],
              color: {
                up: '#10b981',
                down: '#ef4444',
                unchanged: '#9ca3af'
              }
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: { display: false }
            },
            scales: {
              x: {
                type: 'time',
                grid: { color: '#374151' },
                ticks: { color: '#9ca3af' }
              },
              y: {
                grid: { color: '#374151' },
                ticks: { 
                  color: '#9ca3af',
                  callback: function(value) {
                    return '$' + value.toFixed(0);
                  }
                }
              }
            }
          }
        });
        
        console.log('✅ Test chart created successfully');
      } catch (error) {
        console.error('❌ Chart creation failed:', error);
      }
    }

    function addMockCandles() {
      if (!testChart) {
        alert('Create test chart first');
        return;
      }
      
      if (!window.mockCandles) {
        alert('Generate mock data first');
        return;
      }
      
      testChart.data.datasets[0].data = window.mockCandles;
      testChart.update();
      
      console.log('✅ Mock candles added to chart');
    }

    // Auto-run basic tests
    document.addEventListener('DOMContentLoaded', () => {
      testChartJS();
      testCandlestickPlugin();
    });
  </script>
</body>
</html>
