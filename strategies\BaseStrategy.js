// Base strategy class that all strategies should extend
export class BaseStrategy {
  constructor(config = {}) {
    this.config = {
      accountPercentToUse: 0.5,
      minTrendLookback: 20,
      volatilityLookback: 14,
      baseStopLossPercent: 0.025,
      ...config
    };
    this.priceHistory = [];
  }

  // Update price history - called by bot on each price update
  updatePrice(price) {
    this.priceHistory.push(price);
    // Keep only the last 200 prices to prevent memory issues
    if (this.priceHistory.length > 200) {
      this.priceHistory = this.priceHistory.slice(-200);
    }
  }

  // Must be implemented by each strategy
  shouldEnterTrade(currentPrice, marketData) {
    throw new Error('shouldEnterTrade must be implemented by strategy');
  }

  // Must be implemented by each strategy
  calculateStopLoss(currentPrice, marketData) {
    throw new Error('calculateStopLoss must be implemented by strategy');
  }

  // Must be implemented by each strategy
  getPositionSize(availableCash, currentPrice) {
    throw new Error('getPositionSize must be implemented by strategy');
  }

  // Optional: Check if strategy wants to exit current position
  shouldExitTrade(currentPrice, marketData) {
    // Default implementation: no exit logic
    return { shouldExit: false, reason: 'No exit logic implemented' };
  }

  // Helper: Check if strategy already has a position
  hasPosition(marketData) {
    return marketData.currentPosition && marketData.currentPosition.quantity > 0;
  }

  // Helper: Get current position value
  getPositionValue(currentPrice, marketData) {
    if (!this.hasPosition(marketData)) return 0;
    return marketData.currentPosition.quantity * currentPrice;
  }

  // Helper methods available to all strategies
  calculateSMA(prices, period) {
    if (prices.length < period) return null;
    const sum = prices.slice(-period).reduce((a, b) => a + b, 0);
    return sum / period;
  }

  calculateATR(prices, period = 14) {
    if (prices.length < period + 1) return this.config.baseStopLossPercent;
    
    let atr = 0;
    for (let i = prices.length - period; i < prices.length - 1; i++) {
      const high = Math.max(prices[i], prices[i + 1]);
      const low = Math.min(prices[i], prices[i + 1]);
      const trueRange = high - low;
      atr += trueRange;
    }
    
    const avgTrueRange = atr / period;
    const currentPrice = prices[prices.length - 1];
    
    // Convert ATR to percentage and cap it
    const atrPercent = (avgTrueRange / currentPrice) * 2;
    return Math.min(Math.max(atrPercent, 0.015), 0.08);
  }

  isUptrend(prices, period) {
    if (prices.length < period) return true;
    
    const sma = this.calculateSMA(prices, period);
    const currentPrice = prices[prices.length - 1];
    
    // Price above SMA and SMA is rising
    const olderSma = this.calculateSMA(prices.slice(0, -5), period);
    return currentPrice > sma && sma > olderSma;
  }

  getName() {
    return this.constructor.name;
  }

  getDescription() {
    return 'Base strategy class - should be overridden by specific strategies';
  }

  getConfigSchema() {
    return {
      accountPercentToUse: {
        type: 'number',
        label: 'Account % to Use',
        min: 0.1,
        max: 1,
        step: 0.1,
        default: 0.5,
        description: 'Percentage of available cash to use for each trade'
      }
    };
  }
}

