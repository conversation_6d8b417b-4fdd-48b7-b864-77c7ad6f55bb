import { BaseStrategy } from './BaseStrategy.js';

export class VolatilityWeightedStrategy extends BaseStrategy {
  constructor(config = {}) {
    super({
      riskFactor: 0.02, // risk 2% of available cash per trade
      ...config
    });
  }

  shouldEnterTrade(currentPrice) {
    // For this strategy, we assume entry when market shows a trend.
    // Feel free to refine this logic based on your market data.
    const inTrend = this.isUptrend(this.priceHistory, this.config.minTrendLookback);
    return {
      shouldEnter: inTrend,
      reason: inTrend
        ? 'Market trending; entering with volatility-based position sizing'
        : 'Market not trending'
    };
  }

  calculateStopLoss(currentPrice) {
    const stopPercent = this.calculateATR(this.priceHistory);
    return {
      stopPercent,
      stopPrice: currentPrice * (1 - stopPercent),
      reason: `ATR-based stop at ${Math.round(stopPercent * 10000) / 100}%`
    };
  }

  getPositionSize(availableCash, currentPrice) {
    const { riskFactor } = this.config;
    const atr = this.calculateATR(this.priceHistory);
    const positionSize = (availableCash * riskFactor) / (atr * currentPrice);
    const dollarAmount = positionSize * currentPrice;
    return {
      quantity: positionSize,
      dollarAmount,
      reason: `Risk-adjusted size using a risk factor of ${riskFactor * 100}%`
    };
  }

  getDescription() {
    return 'Position size is scaled according to market volatility. Lower volatility yields a larger position and vice versa.';
  }

  getConfigSchema() {
    return {
      ...super.getConfigSchema(),
      riskFactor: {
        type: 'number',
        label: 'Risk Factor',
        min: 0.005,
        max: 0.05,
        step: 0.005,
        default: 0.02,
        description: 'Fraction of available cash to risk per trade (as a decimal)'
      }
    };
  }
}
