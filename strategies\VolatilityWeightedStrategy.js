import { BaseStrategy } from './BaseStrategy.js';

export class VolatilityWeightedStrategy extends BaseStrategy {
  constructor(config = {}) {
    super({
      riskFactor: 0.02, // risk 2% of available cash per trade
      minTrendLookback: 20, // Add missing minTrendLookback
      ...config
    });
  }

  shouldEnterTrade(currentPrice, marketData) {
    // Don't enter if we already have a position
    if (this.hasPosition(marketData)) {
      const position = marketData.currentPosition;
      return {
        shouldEnter: false,
        reason: `Already holding ${position.quantity.toFixed(8)} BTC at avg price $${position.averagePrice.toFixed(2)}`
      };
    }

    // Check if we have enough price history
    if (this.priceHistory.length < this.config.minTrendLookback) {
      return {
        shouldEnter: false,
        reason: `Accumulating price data (${this.priceHistory.length}/${this.config.minTrendLookback} required)`
      };
    }

    // Check for uptrend
    const inTrend = this.isUptrend(this.priceHistory, this.config.minTrendLookback);
    const atr = this.calculateATR(this.priceHistory);
    const volatilityPercent = (atr / currentPrice * 100);

    if (inTrend) {
      return {
        shouldEnter: true,
        reason: `Uptrend detected with ${volatilityPercent.toFixed(1)}% volatility - entering with risk-adjusted position`
      };
    } else {
      return {
        shouldEnter: false,
        reason: `No uptrend detected (volatility: ${volatilityPercent.toFixed(1)}%)`
      };
    }
  }

  calculateStopLoss(currentPrice) {
    const stopPercent = this.calculateATR(this.priceHistory);
    return {
      stopPercent,
      stopPrice: currentPrice * (1 - stopPercent),
      reason: `ATR-based stop at ${Math.round(stopPercent * 10000) / 100}%`
    };
  }

  getPositionSize(availableCash, currentPrice) {
    const { riskFactor } = this.config;
    const atr = this.calculateATR(this.priceHistory);
    const positionSize = (availableCash * riskFactor) / (atr * currentPrice);
    const dollarAmount = positionSize * currentPrice;
    return {
      quantity: positionSize,
      dollarAmount,
      reason: `Risk-adjusted size using a risk factor of ${riskFactor * 100}%`
    };
  }

  getDescription() {
    return 'Position size is scaled according to market volatility. Lower volatility yields a larger position and vice versa.';
  }

  getConfigSchema() {
    return {
      ...super.getConfigSchema(),
      riskFactor: {
        type: 'number',
        label: 'Risk Factor',
        min: 0.005,
        max: 0.05,
        step: 0.005,
        default: 0.02,
        description: 'Fraction of available cash to risk per trade (as a decimal)'
      }
    };
  }
}
