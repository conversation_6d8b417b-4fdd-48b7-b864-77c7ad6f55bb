import { BaseStrategy } from './BaseStrategy.js';

export class TrendFollowingStrategy extends BaseStrategy {
  constructor(config = {}) {
    super({
      accountPercentToUse: 0.5,
      minTrendLookback: 20,
      volatilityLookback: 14,
      baseStopLossPercent: 0.025,
      shortTermPeriod: 5,
      recentHighThreshold: 0.98,
      ...config
    });
  }

  shouldEnterTrade(currentPrice, marketData) {
    if (this.priceHistory.length < this.config.minTrendLookback) {
      return { 
        shouldEnter: false, 
        reason: 'Not enough price history for analysis, waiting...' 
      };
    }
    
    // Check if we're in an uptrend
    if (!this.isUptrend(this.priceHistory, this.config.minTrendLookback)) {
      return { 
        shouldEnter: false, 
        reason: 'Market not in uptrend, waiting for better conditions' 
      };
    }
    
    // Check if price is above short-term moving average
    const shortSMA = this.calculateSMA(this.priceHistory, this.config.shortTermPeriod);
    if (currentPrice < shortSMA) {
      return { 
        shouldEnter: false, 
        reason: 'Price below short-term average, waiting for pullback to end' 
      };
    }
    
    // Additional filter: avoid buying at recent highs
    const recentHigh = Math.max(...this.priceHistory.slice(-10));
    if (currentPrice > recentHigh * this.config.recentHighThreshold) {
      return { 
        shouldEnter: false, 
        reason: 'Price too close to recent high, waiting for better entry' 
      };
    }
    
    return { 
      shouldEnter: true, 
      reason: 'All trend-following conditions met' 
    };
  }

  calculateStopLoss(currentPrice, marketData) {
    // Use dynamic ATR-based stop loss
    const dynamicStopPercent = this.calculateATR(this.priceHistory, this.config.volatilityLookback);
    return {
      stopPercent: dynamicStopPercent,
      stopPrice: currentPrice * (1 - dynamicStopPercent),
      reason: `ATR-based dynamic stop: ${(dynamicStopPercent * 100).toFixed(2)}%`
    };
  }

  getPositionSize(availableCash, currentPrice) {
    const toSpend = availableCash * this.config.accountPercentToUse;
    return {
      quantity: toSpend / currentPrice,
      dollarAmount: toSpend,
      reason: `Using ${(this.config.accountPercentToUse * 100)}% of available cash`
    };
  }

  getDescription() {
    return 'Advanced strategy that only buys during uptrends, uses dynamic ATR-based stop losses, and avoids buying at recent highs. Better for volatile markets.';
  }

  getConfigSchema() {
    return {
      accountPercentToUse: {
        type: 'number',
        label: 'Account % to Use',
        min: 0.1,
        max: 1,
        step: 0.1,
        default: 0.5,
        description: 'Percentage of available cash to use for each trade'
      },
      minTrendLookback: {
        type: 'number',
        label: 'Trend Lookback',
        min: 10,
        max: 50,
        step: 1,
        default: 20,
        description: 'Number of periods to look back for trend analysis'
      },
      volatilityLookback: {
        type: 'number',
        label: 'Volatility Lookback',
        min: 5,
        max: 30,
        step: 1,
        default: 14,
        description: 'Number of periods for ATR volatility calculation'
      },
      shortTermPeriod: {
        type: 'number',
        label: 'Short Term Period',
        min: 3,
        max: 10,
        step: 1,
        default: 5,
        description: 'Short-term moving average period'
      },
      recentHighThreshold: {
        type: 'number',
        label: 'Recent High Threshold',
        min: 0.9,
        max: 1.0,
        step: 0.01,
        default: 0.98,
        description: 'Avoid buying above this % of recent high'
      }
    };
  }
}
