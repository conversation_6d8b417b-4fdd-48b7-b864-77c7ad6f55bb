import { BaseStrategy } from './BaseStrategy.js';

export class BuyTheDipStrategy extends BaseStrategy {
  constructor(config = {}) {
    super({
      dipThreshold: 0.05, // 5% drop
      dipLookback: 5,
      minTrendLookback: 20, // Add missing minTrendLookback
      ...config
    });
  }

  shouldEnterTrade(currentPrice, marketData) {
    // Don't enter if we already have a position
    if (this.hasPosition(marketData)) {
      const position = marketData.currentPosition;
      return {
        shouldEnter: false,
        reason: `Already holding ${position.quantity.toFixed(8)} BTC at avg price $${position.averagePrice.toFixed(2)}`
      };
    }

    const { dipThreshold, dipLookback, minTrendLookback } = this.config;
    const requiredHistory = Math.max(dipLookback, minTrendLookback);

    if (this.priceHistory.length < requiredHistory) {
      return {
        shouldEnter: false,
        reason: `Accumulating price data (${this.priceHistory.length}/${requiredHistory} required)`
      };
    }

    const recentPrices = this.priceHistory.slice(-dipLookback);
    const maxRecentPrice = Math.max(...recentPrices);
    const dropPercent = (maxRecentPrice - currentPrice) / maxRecentPrice;
    const droppedEnough = dropPercent >= dipThreshold;
    const inUptrend = this.isUptrend(this.priceHistory, minTrendLookback);

    if (droppedEnough && inUptrend) {
      return {
        shouldEnter: true,
        reason: `Dip detected: price dropped ${(dropPercent * 100).toFixed(1)}% from recent high $${maxRecentPrice.toFixed(2)} in uptrend`
      };
    }

    if (!droppedEnough) {
      const neededDrop = (dipThreshold * 100).toFixed(1);
      const currentDrop = (dropPercent * 100).toFixed(1);
      return {
        shouldEnter: false,
        reason: `Waiting for ${neededDrop}% dip (current: ${currentDrop}% from high $${maxRecentPrice.toFixed(2)})`
      };
    }

    if (!inUptrend) {
      return {
        shouldEnter: false,
        reason: `Dip detected but not in uptrend: ${(dropPercent * 100).toFixed(1)}% drop from $${maxRecentPrice.toFixed(2)}`
      };
    }

    return {
      shouldEnter: false,
      reason: 'Monitoring for dip opportunities'
    };
  }

  calculateStopLoss(currentPrice) {
    const stopPercent = this.calculateATR(this.priceHistory);
    return {
      stopPercent,
      stopPrice: currentPrice * (1 - stopPercent),
      reason: `ATR-based stop at ${Math.round(stopPercent * 10000) / 100}%`
    };
  }

  getPositionSize(availableCash, currentPrice) {
    return super.getPositionSize(availableCash, currentPrice);
  }

  getDescription() {
    return 'Enters a trade when price dips significantly but the broader trend is still up. Good for pullback entries in bull markets.';
  }

  getConfigSchema() {
    return {
      ...super.getConfigSchema(),
      dipThreshold: {
        type: 'number',
        label: 'Dip % Threshold',
        min: 0.01,
        max: 0.2,
        step: 0.01,
        default: 0.05,
        description: 'Percentage drop from recent highs to trigger entry'
      },
      dipLookback: {
        type: 'number',
        label: 'Lookback Period',
        min: 2,
        max: 20,
        default: 5,
        description: 'Number of recent candles to use for dip detection'
      }
    };
  }
}
