import { BaseStrategy } from './BaseStrategy.js';

export class BuyTheDipStrategy extends BaseStrategy {
  constructor(config = {}) {
    super({
      dipThreshold: 0.05, // 5% drop
      dipLookback: 5,
      ...config
    });
  }

  shouldEnterTrade(currentPrice) {
    const { dipThreshold, dipLookback, minTrendLookback } = this.config;

    if (this.priceHistory.length < Math.max(dipLookback, minTrendLookback)) {
      return { shouldEnter: false };
    }

    const recentPrices = this.priceHistory.slice(-dipLookback);
    const maxRecentPrice = Math.max(...recentPrices);
    const droppedEnough = (maxRecentPrice - currentPrice) / maxRecentPrice >= dipThreshold;
    const inUptrend = this.isUptrend(this.priceHistory, minTrendLookback);

    return {
      shouldEnter: droppedEnough && inUptrend,
      reason: droppedEnough && inUptrend
        ? `Price dropped ${Math.round((maxRecentPrice - currentPrice) / maxRecentPrice * 100)}% in an uptrend`
        : 'Dip or trend condition not met'
    };
  }

  calculateStopLoss(currentPrice) {
    const stopPercent = this.calculateATR(this.priceHistory);
    return {
      stopPercent,
      stopPrice: currentPrice * (1 - stopPercent),
      reason: `ATR-based stop at ${Math.round(stopPercent * 10000) / 100}%`
    };
  }

  getPositionSize(availableCash, currentPrice) {
    return super.getPositionSize(availableCash, currentPrice);
  }

  getDescription() {
    return 'Enters a trade when price dips significantly but the broader trend is still up. Good for pullback entries in bull markets.';
  }

  getConfigSchema() {
    return {
      ...super.getConfigSchema(),
      dipThreshold: {
        type: 'number',
        label: 'Dip % Threshold',
        min: 0.01,
        max: 0.2,
        step: 0.01,
        default: 0.05,
        description: 'Percentage drop from recent highs to trigger entry'
      },
      dipLookback: {
        type: 'number',
        label: 'Lookback Period',
        min: 2,
        max: 20,
        default: 5,
        description: 'Number of recent candles to use for dip detection'
      }
    };
  }
}
