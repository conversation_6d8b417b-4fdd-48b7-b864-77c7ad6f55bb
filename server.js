import http from 'http';
import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
dotenv.config();

import WebSocketPkg from 'ws';
const { Server: WebSocketServer } = WebSocketPkg;

import { startBot, stopBot, getBotStatus, getBotStats, setStrategy, getCurrentStrategy, setMaxTradingBalance, getMaxTradingBalance } from './bot.js';
import { STRATEGIES } from './strategies/index.js';
import { RobinhoodAPI } from './robinhoodClient.js';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Dynamically discover available strategies
function discoverStrategies() {
  const strategiesPath = path.join(__dirname, 'strategies');
  const strategies = [];
  
  try {
    const files = fs.readdirSync(strategiesPath);
    
    for (const file of files) {
      if (file.endsWith('.js') && file !== 'index.js' && file !== 'BaseStrategy.js') {
        const strategyName = file.replace('.js', '');
        
        // Convert class name to kebab-case
        const id = strategyName
          .replace('Strategy', '')
          .replace(/([A-Z])/g, (match, letter, index) => {
            return index === 0 ? letter.toLowerCase() : '-' + letter.toLowerCase();
          });
        
        try {
          // Dynamically import the strategy to get its schema
          import(`./strategies/${file}`).then(module => {
            const StrategyClass = module[strategyName];
            if (StrategyClass && StrategyClass.prototype) {
              const tempInstance = new StrategyClass();
              
              strategies.push({
                id,
                className: strategyName,
                displayName: tempInstance.getName ? tempInstance.getName().replace('Strategy', '') : strategyName.replace('Strategy', ''),
                description: tempInstance.getDescription ? tempInstance.getDescription() : `${strategyName} trading strategy`,
                config: tempInstance.getConfigSchema ? tempInstance.getConfigSchema() : {}
              });
            }
          }).catch(err => {
            console.warn(`Failed to load strategy ${strategyName}:`, err.message);
          });
        } catch (error) {
          console.warn(`Failed to analyze strategy ${strategyName}:`, error.message);
        }
      }
    }
  } catch (error) {
    console.error('Failed to discover strategies:', error);
  }
  
  return strategies;
}

// Cache discovered strategies
let availableStrategies = [];

// Initialize strategies on startup - make this synchronous and more robust
async function initializeStrategies() {
  const strategiesPath = path.join(__dirname, 'strategies');
  
  try {
    const files = fs.readdirSync(strategiesPath);
    console.log('Found strategy files:', files);
    
    for (const file of files) {
      if (file.endsWith('.js') && file !== 'index.js' && file !== 'BaseStrategy.js' && file !== 'PortfolioManager.js') {
        const strategyName = file.replace('.js', '');
        
        // Convert class name to kebab-case
        const id = strategyName
          .replace('Strategy', '')
          .replace(/([A-Z])/g, (match, letter, index) => {
            return index === 0 ? letter.toLowerCase() : '-' + letter.toLowerCase();
          });
        
        try {
          // Dynamically import the strategy
          const module = await import(`./strategies/${file}`);
          const StrategyClass = module[strategyName];
          
          if (StrategyClass && StrategyClass.prototype) {
            const tempInstance = new StrategyClass();
            
            availableStrategies.push({
              id,
              className: strategyName,
              displayName: tempInstance.getName ? tempInstance.getName().replace('Strategy', '') : strategyName.replace('Strategy', ''),
              description: tempInstance.getDescription ? tempInstance.getDescription() : `${strategyName} trading strategy`,
              config: tempInstance.getConfigSchema ? tempInstance.getConfigSchema() : {}
            });
            
            console.log(`Loaded strategy: ${id} (${strategyName})`);
          }
        } catch (error) {
          console.warn(`Failed to load strategy ${strategyName}:`, error.message);
        }
      }
    }
    
    console.log(`Successfully discovered ${availableStrategies.length} strategies:`, availableStrategies.map(s => s.id));
  } catch (error) {
    console.error('Failed to discover strategies:', error);
  }
}

// Make sure this runs before server starts
await initializeStrategies();

const app = express();
const server = http.createServer(app);
const wss = new WebSocketServer({ server });

// Serve static files from public/
app.use(express.static(path.join(__dirname, 'public')));

// API endpoints MUST come before the fallback route
app.post('/api/remote', express.json(), async (req, res) => {
  const { action } = req.body;
  
  try {
    if (action === 'start') {
      await startBot();
    } else if (action === 'stop') {
      await stopBot();
    }
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/portfolio', async (req, res) => {
  try {
    const strategy = getCurrentStrategy();
    
    // If no strategy is set or it's not a portfolio manager, initialize one
    if (!strategy || strategy.constructor.name !== 'PortfolioManager') {
      // Initialize default portfolio manager
      const defaultConfig = {
        strategies: [
          { name: 'trend-following', allocation: 0.4, config: { accountPercentToUse: 1.0 } },
          { name: 'mean-reversion', allocation: 0.3, config: { accountPercentToUse: 1.0 } },
          { name: 'breakout', allocation: 0.3, config: { accountPercentToUse: 1.0 } }
        ],
        rebalanceFrequency: 24 * 60 * 60 * 1000
      };
      
      setStrategy('portfolio-manager', defaultConfig);
      const newStrategy = getCurrentStrategy();
      
      if (!newStrategy) {
        return res.status(500).json({ error: 'Failed to initialize portfolio manager' });
      }
    }
    
    const currentStrategy = getCurrentStrategy();
    const stats = currentStrategy.getStats();
    const allocations = {};
    
    // Get current allocations
    if (currentStrategy.strategyAllocations) {
      currentStrategy.strategyAllocations.forEach((allocation, name) => {
        allocations[name] = allocation;
      });
    }
    
    // Get total available balance and apply max trading balance limit
    const botStats = await getBotStats();
    const actualBalance = parseFloat(botStats.balance || 0) + parseFloat(botStats.btcHoldings || 0) * parseFloat(botStats.btcPrice || 0);
    const maxTradingBalance = getMaxTradingBalance();
    const effectiveBalance = maxTradingBalance !== null ? Math.min(actualBalance, maxTradingBalance) : actualBalance;
    
    res.json({
      strategies: stats.strategies || {},
      allocations,
      totalBalance: effectiveBalance.toFixed(2),
      actualBalance: actualBalance.toFixed(2),
      maxTradingBalance: maxTradingBalance,
      totalTrades: stats.totalTrades || 0,
      lastRebalance: stats.lastRebalance || Date.now(),
      rebalanceFrequency: stats.rebalanceFrequency || '24 hours'
    });
    
  } catch (error) {
    console.error('Portfolio endpoint error:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/portfolio/strategy', express.json(), async (req, res) => {
  const { strategyName, config, enabled } = req.body;
  
  try {
    const strategy = getCurrentStrategy();
    if (strategy && strategy.constructor.name === 'PortfolioManager') {
      // Update strategy configuration
      const strategyInstance = strategy.strategyInstances.get(strategyName);
      if (strategyInstance) {
        Object.assign(strategyInstance.config, config);
        
        // Handle enable/disable logic here if needed
        
        res.json({ success: true, message: `Updated ${strategyName} configuration` });
      } else {
        res.status(404).json({ error: 'Strategy not found' });
      }
    } else {
      res.status(500).json({ error: 'Portfolio manager not active' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/strategies', (req, res) => {
  try {
    console.log(`Serving ${availableStrategies.length} strategies`);
    res.json({ 
      strategies: availableStrategies,
      count: availableStrategies.length 
    });
  } catch (error) {
    console.error('Error serving strategies:', error);
    res.status(500).json({ 
      error: 'Failed to load strategies',
      strategies: [],
      count: 0 
    });
  }
});

app.post('/api/max-balance', express.json(), async (req, res) => {
  const { maxBalance } = req.body;
  
  try {
    const result = setMaxTradingBalance(maxBalance);
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/max-balance', async (req, res) => {
  try {
    const maxBalance = getMaxTradingBalance();
    res.json({ maxBalance });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/portfolio', express.json(), async (req, res) => {
  const { strategies, config = {} } = req.body;
  
  try {
    // Create portfolio manager configuration
    const portfolioConfig = {
      strategies: strategies || [
        {
          name: 'trend-following',
          allocation: 0.4,
          config: { accountPercentToUse: 1.0, minTrendLookback: 20, volatilityLookback: 14 }
        },
        {
          name: 'mean-reversion', 
          allocation: 0.3,
          config: { accountPercentToUse: 1.0 }
        },
        {
          name: 'breakout',
          allocation: 0.3, 
          config: { accountPercentToUse: 1.0 }
        }
      ],
      rebalanceFrequency: config.rebalanceFrequency || 24 * 60 * 60 * 1000, // 24 hours
      performanceWindow: config.performanceWindow || 7 * 24 * 60 * 60 * 1000, // 7 days
      minAllocation: config.minAllocation || 0.1,
      maxAllocation: config.maxAllocation || 0.6,
      rebalanceThreshold: config.rebalanceThreshold || 0.1,
      ...config
    };
    
    const result = setStrategy('portfolio-manager', portfolioConfig);
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/portfolio/stats', async (req, res) => {
  try {
    const strategy = getCurrentStrategy();
    if (strategy && strategy.constructor.name === 'PortfolioManager') {
      const stats = strategy.getStats();
      res.json(stats);
    } else {
      res.json({ error: 'Portfolio manager not active' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/portfolio/status', async (req, res) => {
  try {
    const strategy = getCurrentStrategy();
    if (strategy && strategy.constructor.name === 'PortfolioManager') {
      const stats = strategy.getStats();

      // Get current price for real-time status updates
      const botStats = await getBotStats();
      const currentPrice = parseFloat(botStats.btcPrice || 0);

      // Update strategy status with current market conditions
      if (currentPrice > 0) {
        const totalBalance = parseFloat(botStats.balance || 0);
        strategy.strategyInstances.forEach((strategyInstance, name) => {
          const allocation = strategy.strategyAllocations.get(name);
          const strategyBalance = totalBalance * allocation;

          if (strategyBalance >= 10) {
            const decision = strategyInstance.shouldEnterTrade(currentPrice, {
              availableBalance: strategyBalance
            });

            const status = strategy.strategyStatus.get(name) || {};
            strategy.strategyStatus.set(name, {
              ...status,
              lastDecision: decision.shouldEnter,
              lastDecisionTime: Date.now(),
              lastDecisionReason: decision.reason || 'No reason provided',
              nextSignal: strategy.getNextSignalDescription(strategyInstance, name, currentPrice, decision)
            });
          }
        });

        // Get updated stats with fresh status
        const updatedStats = strategy.getStats();
        res.json({
          ...updatedStats,
          currentPrice: currentPrice,
          lastUpdate: Date.now()
        });
      } else {
        res.json(stats);
      }
    } else {
      res.json({ error: 'Portfolio manager not active' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/portfolio/strategy/toggle', express.json(), async (req, res) => {
  const { strategyName, enabled } = req.body;
  
  try {
    const strategy = getCurrentStrategy();
    if (strategy && strategy.constructor.name === 'PortfolioManager') {
      
      if (enabled) {
        // Add strategy to portfolio
        const strategyConfig = availableStrategies.find(s => s.id === strategyName);
        if (!strategyConfig) {
          return res.status(404).json({ error: 'Strategy not found' });
        }
        
        // Create default config for the strategy
        const defaultConfig = {};
        Object.entries(strategyConfig.config).forEach(([key, schema]) => {
          defaultConfig[key] = schema.default;
        });
        
        // Add to portfolio with cautious default allocation
        const currentStrategies = strategy.config.strategies;
        const newAllocation = Math.max(0.05, strategy.config.minAllocation || 0.05); // Minimum 5%

        // Calculate total current allocation
        const totalCurrentAllocation = currentStrategies.reduce((sum, s) => sum + s.allocation, 0);

        // Reduce existing allocations proportionally
        const reductionFactor = (totalCurrentAllocation - newAllocation) / totalCurrentAllocation;
        currentStrategies.forEach(s => {
          s.allocation = s.allocation * reductionFactor;
        });
        
        // Add new strategy
        currentStrategies.push({
          name: strategyName,
          allocation: newAllocation,
          config: { accountPercentToUse: 1.0, ...defaultConfig }
        });
        
        // Reinitialize portfolio to sync runtime allocations
        strategy.initializeStrategies();

        console.log(`Strategy ${strategyName} enabled with ${(newAllocation * 100).toFixed(1)}% allocation`);
        
      } else {
        // Remove strategy from portfolio
        const currentStrategies = strategy.config.strategies;
        const strategyIndex = currentStrategies.findIndex(s => s.name === strategyName);
        
        if (strategyIndex === -1) {
          return res.status(404).json({ error: 'Strategy not active in portfolio' });
        }
        
        // Remove the strategy
        const removedAllocation = currentStrategies[strategyIndex].allocation;
        currentStrategies.splice(strategyIndex, 1);
        
        // Redistribute allocation among remaining strategies
        if (currentStrategies.length > 0) {
          const redistributeAmount = removedAllocation / currentStrategies.length;
          currentStrategies.forEach(s => {
            s.allocation += redistributeAmount;
          });
        }
        
        // Remove from strategy instances
        strategy.strategyInstances.delete(strategyName);
        strategy.strategyAllocations.delete(strategyName);
        strategy.strategyPerformance.delete(strategyName);
        strategy.strategyStatus.delete(strategyName);

        console.log(`Strategy ${strategyName} disabled, ${(removedAllocation * 100).toFixed(1)}% allocation redistributed to remaining strategies`);
      }
      
      // Save configuration
      saveConfig();
      
      res.json({ 
        success: true, 
        message: `Strategy ${strategyName} ${enabled ? 'enabled' : 'disabled'}`,
        strategies: strategy.config.strategies
      });
      
    } else {
      res.status(500).json({ error: 'Portfolio manager not active' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/portfolio/strategy/config', express.json(), async (req, res) => {
  const { strategyName, config } = req.body;
  
  try {
    const strategy = getCurrentStrategy();
    if (strategy && strategy.constructor.name === 'PortfolioManager') {
      
      // Update strategy configuration in portfolio config
      const strategyConfig = strategy.config.strategies.find(s => s.name === strategyName);
      if (strategyConfig) {
        Object.assign(strategyConfig.config, config);
        
        // Update the actual strategy instance
        const strategyInstance = strategy.strategyInstances.get(strategyName);
        if (strategyInstance) {
          Object.assign(strategyInstance.config, config);
        }
        
        // Save configuration
        saveConfig();
        
        res.json({ success: true, message: `Updated ${strategyName} configuration` });
      } else {
        res.status(404).json({ error: 'Strategy not found in portfolio' });
      }
    } else {
      res.status(500).json({ error: 'Portfolio manager not active' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/strategy/single', express.json(), async (req, res) => {
  const { strategyName, config } = req.body;
  
  try {
    // Switch to single strategy mode (bypass portfolio manager)
    const result = setStrategy(strategyName, config);
    
    // Save configuration
    const configToSave = {
      maxTradingBalance: maxTradingBalance,
      singleStrategy: { name: strategyName, config },
      lastUpdated: new Date().toISOString()
    };
    
    fs.writeFileSync(CONFIG_FILE, JSON.stringify(configToSave, null, 2));
    
    res.json({ 
      success: true, 
      message: `Switched to single strategy mode: ${strategyName}`,
      mode: 'single'
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/strategy/portfolio', express.json(), async (req, res) => {
  const { strategies, config } = req.body;
  
  try {
    // Switch back to portfolio mode
    const portfolioConfig = {
      strategies: strategies || [
        {
          name: 'trend-following',
          allocation: 0.5,
          config: { accountPercentToUse: 1.0 }
        },
        {
          name: 'mean-reversion',
          allocation: 0.5,
          config: { accountPercentToUse: 1.0 }
        }
      ],
      rebalanceFrequency: config?.rebalanceFrequency || 24 * 60 * 60 * 1000,
      ...config
    };
    
    const result = setStrategy('portfolio-manager', portfolioConfig);
    
    // Save configuration
    saveConfig();
    
    res.json({ 
      success: true, 
      message: 'Switched to portfolio mode',
      mode: 'portfolio'
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/strategy/mode', (req, res) => {
  try {
    const strategy = getCurrentStrategy();
    const mode = strategy && strategy.constructor.name === 'PortfolioManager' ? 'portfolio' : 'single';
    
    res.json({ 
      mode,
      strategyName: strategy ? strategy.getName() : 'None',
      isPortfolio: mode === 'portfolio'
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Fallback route for SPA (index.html) - MUST be last
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public/index.html'));
});

// Add memory management for server-side price storage
let serverPriceHistory = [];
const MAX_SERVER_HISTORY = 1000;

// WebSocket connection with enhanced data
wss.on('connection', (ws) => {
  console.log('Client connected');
  
  // Send initial data including historical prices
  const sendInitialData = async () => {
    try {
      // Get current bot stats
      const stats = await getBotStats();
      
      // Send existing server price history to new client
      if (serverPriceHistory.length > 0) {
        stats.historicalPrices = serverPriceHistory;
        console.log(`Sending ${serverPriceHistory.length} historical price points to new client`);
      } else {
        // If no server history, try to get some initial data
        const historicalPrices = await getHistoricalPrices();
        stats.historicalPrices = historicalPrices;
        // Also populate server history with this initial data
        serverPriceHistory = [...historicalPrices];
      }
      
      // Send data
      ws.send(JSON.stringify(stats));
    } catch (error) {
      console.error('Error sending initial data:', error);
    }
  };
  
  sendInitialData();
  
  // Set up regular price updates
  const interval = setInterval(async () => {
    try {
      // Get latest price from Robinhood
      const bestBidAskResponse = await RobinhoodAPI.getBestBidAsk('BTC-USD');
      
      // Get other bot stats
      const stats = await getBotStats();

      // Check if we have results in the expected format
      if (bestBidAskResponse && bestBidAskResponse.results && bestBidAskResponse.results.length) {
        const currentPriceData = bestBidAskResponse.results[0];
        const currentPrice = parseFloat(currentPriceData.price || currentPriceData.ask_inclusive_of_buy_spread);

        if (currentPrice) {
          stats.btcPrice = currentPrice.toString();

          // Add to server price history with memory management
          const newPricePoint = {
            timestamp: new Date().toISOString(),
            price: currentPrice
          };

          serverPriceHistory.push(newPricePoint);

          if (serverPriceHistory.length > MAX_SERVER_HISTORY) {
            serverPriceHistory = serverPriceHistory.slice(-MAX_SERVER_HISTORY);
          }

          // Include the new price point in the update
          stats.newPricePoint = newPricePoint;

          // Include strategy status updates if portfolio manager is active
          const strategy = getCurrentStrategy();
          if (strategy && strategy.constructor.name === 'PortfolioManager') {
            try {
              // Update strategy price histories with current price
              strategy.updatePrice(currentPrice);

              const totalBalance = parseFloat(stats.balance || 0);

              // Update strategy status with current market conditions
              strategy.strategyInstances.forEach((strategyInstance, name) => {
                const allocation = strategy.strategyAllocations.get(name);
                const strategyBalance = totalBalance * allocation;

                if (strategyBalance >= 10) {
                  const decision = strategyInstance.shouldEnterTrade(currentPrice, {
                    availableBalance: strategyBalance
                  });

                  const status = strategy.strategyStatus.get(name) || {};
                  strategy.strategyStatus.set(name, {
                    ...status,
                    lastDecision: decision.shouldEnter,
                    lastDecisionTime: Date.now(),
                    lastDecisionReason: decision.reason || 'No reason provided',
                    nextSignal: strategy.getNextSignalDescription(strategyInstance, name, currentPrice, decision)
                  });
                }
              });

              // Include strategy status in the update
              stats.strategyStatus = strategy.getStats();
            } catch (error) {
              console.error('Error updating strategy status:', error);
            }
          }

          console.log(`Sending updated BTC price: $${currentPrice}`);
        }
      }
      
      // Send update
      ws.send(JSON.stringify(stats));
    } catch (error) {
      console.error('Error sending update:', error);
    }
  }, 5000); // Update every 5 seconds
  
  ws.on('close', () => {
    console.log('Client disconnected');
    clearInterval(interval);
  });
});

async function sendBotStats(ws) {
  try {
    const stats = await getBotStats();
    ws.send(JSON.stringify(stats));
  } catch (error) {
    console.error('Error sending stats:', error);
  }
}

// Function to get historical price data from Robinhood
async function getHistoricalPrices() {
  try {
    // First get the current price
    const bestBidAskResponse = await RobinhoodAPI.getBestBidAsk('BTC-USD');
    
    // Check if we have results in the expected format
    if (!bestBidAskResponse || !bestBidAskResponse.results || !bestBidAskResponse.results.length) {
      console.error('Failed to get current BTC price from Robinhood');
      return [];
    }
    
    const currentPriceData = bestBidAskResponse.results[0];
    const currentPrice = parseFloat(currentPriceData.price || currentPriceData.ask_inclusive_of_buy_spread);
    const timestamp = currentPriceData.timestamp || new Date().toISOString();
    
    console.log(`Current BTC price from Robinhood: $${currentPrice}`);
    
    // For historical data, we can try to get recent orders to see price history
    const recentOrders = await RobinhoodAPI.getHistoricalOrders('BTC-USD', 100);
    
    // Start with the current price
    const prices = [{
      timestamp: timestamp,
      price: currentPrice
    }];
    
    // Add prices from recent orders if available
    if (recentOrders && recentOrders.results && recentOrders.results.length) {
      console.log(`Found ${recentOrders.results.length} historical orders`);
      
      recentOrders.results.forEach(order => {
        if (order.executions && order.executions.length) {
          order.executions.forEach(execution => {
            prices.push({
              timestamp: execution.timestamp,
              price: parseFloat(execution.effective_price)
            });
          });
        }
      });
    }
    
    // Sort by timestamp (oldest first)
    prices.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
    
    return prices;
  } catch (error) {
    console.error('Error fetching historical prices from Robinhood:', error);
    return [];
  }
}

const PORT = process.env.PORT || 8182;
server.listen(PORT, () => {
  console.log(`✅ Server + WebSocket running at http://localhost:${PORT}`);
});
