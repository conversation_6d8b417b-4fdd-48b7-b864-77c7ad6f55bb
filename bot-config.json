{"maxTradingBalance": 100, "portfolio": {"strategies": [{"name": "trend-following", "allocation": 0.*****************, "config": {"accountPercentToUse": 1}}, {"name": "mean-reversion", "allocation": 0.****************, "config": {"accountPercentToUse": 1}}, {"name": "breakout", "allocation": 0.****************, "config": {"accountPercentToUse": 1}}, {"name": "buy-the-dip", "allocation": 0.*****************, "config": {"accountPercentToUse": 0.5, "dipThreshold": 0.05, "dipLookback": 5}}, {"name": "moving-average-crossover", "allocation": 0.*****************, "config": {"accountPercentToUse": 0.5, "shortPeriod": 5, "longPeriod": 20}}, {"name": "range-trading", "allocation": 0.125, "config": {"accountPercentToUse": 0.5, "rangeLookback": 20, "entryBufferPercent": 0.02}}, {"name": "trailing-entry", "allocation": 0.125, "config": {"accountPercentToUse": 0.5, "trailingLookback": 10, "recoveryPercent": 0.03}}, {"name": "volatility-weighted", "allocation": 0.125, "config": {"accountPercentToUse": 0.5, "riskFactor": 0.02}}], "rebalanceFrequency": ********, "performanceWindow": *********, "minAllocation": 0.05, "maxAllocation": 0.6, "rebalanceThreshold": 0.1}, "lastUpdated": "2025-07-16T21:42:50.324Z"}