{"maxTradingBalance": 90, "portfolio": {"strategies": [{"name": "mean-reversion", "allocation": 0.****************, "config": {"accountPercentToUse": 1, "timeframe": "5m", "meanPeriod": 20, "deviationThreshold": 0.04}}, {"name": "breakout", "allocation": 0.****************, "config": {"accountPercentToUse": 1, "timeframe": "5m", "breakoutLookback": 20, "confirmRetest": false}}, {"name": "moving-average-crossover", "allocation": 0.*****************, "config": {"accountPercentToUse": 0.5, "timeframe": "5m", "shortPeriod": 5, "longPeriod": 20}}, {"name": "range-trading", "allocation": 0.*****************, "config": {"accountPercentToUse": 0.5, "timeframe": "5m", "rangeLookback": 20, "entryBufferPercent": 0.02}}, {"name": "trailing-entry", "allocation": 0.*****************, "config": {"accountPercentToUse": 0.5, "timeframe": "5m", "trailingLookback": 10, "recoveryPercent": 0.03}}, {"name": "trend-following", "allocation": 0.*****************, "config": {"accountPercentToUse": 0.5, "minTrendLookback": 20, "volatilityLookback": 14, "shortTermPeriod": 5, "recentHighThreshold": 0.98, "timeframe": "5m"}}, {"name": "buy-the-dip", "allocation": 0.0475, "config": {"accountPercentToUse": 0.5, "timeframe": "5m", "dipThreshold": 0.05, "dipLookback": 5, "minTrendLookback": 20}}, {"name": "volatility-weighted", "allocation": 0.05, "config": {"accountPercentToUse": 0.5, "timeframe": "5m", "riskFactor": 0.02, "minTrendLookback": 20}}], "rebalanceFrequency": ********, "performanceWindow": *********, "minAllocation": 0.05, "maxAllocation": 0.6, "rebalanceThreshold": 0.1}, "lastUpdated": "2025-07-17T15:56:04.014Z", "positions": {"positions": {}, "orders": {}, "savedAt": *************}, "tradeHistory": {"trades": [], "savedAt": *************}}