* { 
  margin: 0; 
  padding: 0; 
  box-sizing: border-box; 
}

body { 
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  background: #000000;
  color: #ffffff;
  min-height: 100vh;
  line-height: 1.4;
  font-size: 16px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 32px;
  padding: 24px 0;
}

.header h1 {
  font-size: 32px;
  font-weight: 300;
  margin-bottom: 8px;
  color: #ffffff;
  letter-spacing: -0.5px;
}

.header p {
  color: #9ca3af;
  font-size: 16px;
  font-weight: 400;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: 12px;
  margin-bottom: 24px;
}

.stat-card {
  background: #1a1a1a;
  border: 1px solid #2a2a2a;
  border-radius: 6px;
  padding: 20px;
  transition: border-color 0.2s ease;
}

.stat-card:hover {
  border-color: #3a3a3a;
}

.stat-card h3 {
  color: #9ca3af;
  font-size: 13px;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: 400;
  color: #ffffff;
  line-height: 1.2;
}

.status-running { 
  color: #00C805; 
  font-weight: 500;
}

.status-stopped { 
  color: #FF6B35; 
  font-weight: 500;
}

.positive { 
  color: #00C805; 
}

.negative { 
  color: #FF6B35; 
}

.controls {
  background: #1a1a1a;
  border: 1px solid #2a2a2a;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 24px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.toggle-btn {
  background: linear-gradient(45deg, #10b981, #059669);
  color: #ffffff;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  font-size: 16px;
  transition: all 0.2s ease;
  align-self: flex-start;
}

.toggle-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.balance-control {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.balance-control label {
  color: #9ca3af;
  font-size: 14px;
  font-weight: 400;
}

.balance-slider {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: #374151;
  outline: none;
  cursor: pointer;
}

.balance-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #10b981;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.balance-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #10b981;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.balance-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #6b7280;
}

.balance-min, .balance-max {
  font-weight: 400;
}

.strategy-section {
  background: #1a1a1a;
  border: 1px solid #2a2a2a;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 24px;
}

.strategy-section h3 {
  color: #9ca3af;
  font-size: 13px;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 16px;
}

.strategy-selector {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.strategy-selector select {
  background: #374151;
  border: 1px solid #4b5563;
  border-radius: 4px;
  padding: 8px 12px;
  color: #f9fafb;
  min-width: 200px;
  cursor: pointer;
}

.strategy-selector select:focus {
  outline: none;
  border-color: #10b981;
}

.apply-btn {
  background: #00C805;
  color: #ffffff;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.apply-btn:hover {
  background: #00b004;
}

.strategy-config {
  border-top: 1px solid #2a2a2a;
  padding-top: 20px;
  margin-bottom: 20px;
}

.strategy-config h4 {
  margin-bottom: 16px;
  color: #9ca3af;
  font-size: 13px;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.config-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.config-item label {
  color: #9ca3af;
  font-size: 12px;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.config-item input {
  background: #374151;
  border: 1px solid #4b5563;
  border-radius: 4px;
  padding: 8px 12px;
  color: #f9fafb;
  font-size: 14px;
}

.config-item input:focus {
  outline: none;
  border-color: #10b981;
}

.logs-section {
  background: #1a1a1a;
  border: 1px solid #2a2a2a;
  border-radius: 6px;
  padding: 20px;
}

.logs-section h3 {
  color: #9ca3af;
  font-size: 13px;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 16px;
}

#log {
  background: #0f0f0f;
  border: 1px solid #2a2a2a;
  border-radius: 4px;
  padding: 12px;
  height: 200px;
  overflow-y: auto;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.log-entry {
  margin-bottom: 2px;
  opacity: 0.7;
  font-size: 13px;
}

.log-entry:last-child {
  opacity: 1;
  color: #ffffff;
  font-weight: 500;
}

.chart-section {
  margin-bottom: 24px;
}

.chart-card {
  background: #1a1a1a;
  border: 1px solid #2a2a2a;
  border-radius: 6px;
  padding: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.chart-header h3 {
  color: #9ca3af;
  font-size: 13px;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.chart-timeframe {
  display: flex;
  gap: 4px;
}

.timeframe-btn {
  background: transparent;
  border: 1px solid #374151;
  color: #9ca3af;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.timeframe-btn:hover {
  border-color: #4b5563;
  color: #f9fafb;
}

.timeframe-btn.active {
  background: #10b981;
  border-color: #10b981;
  color: #ffffff;
}

.chart-container {
  position: relative;
  height: 400px;
  width: 100%;
}

.chart-tooltip {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid #2a2a2a;
  border-radius: 4px;
  color: #ffffff;
  font-size: 12px;
  padding: 8px 12px;
  pointer-events: none;
  position: absolute;
  z-index: 100;
}

.chart-tooltip-label {
  font-weight: 500;
  margin-bottom: 4px;
}

.chart-tooltip-value {
  color: #00C805;
}

.chart-tooltip-date {
  color: #9ca3af;
  font-size: 11px;
  margin-top: 4px;
}

.chart-event-marker {
  cursor: pointer;
}

.strategy-mode-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.mode-toggle {
  display: flex;
  background: #374151;
  border-radius: 6px;
  padding: 4px;
}

.mode-btn {
  background: transparent;
  border: none;
  color: #9ca3af;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.mode-btn.active {
  background: #10b981;
  color: white;
}

.mode-section {
  transition: opacity 0.3s ease;
}

.single-strategy-selector {
  background: #1f2937;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.strategy-select {
  background: #374151;
  border: 1px solid #4b5563;
  border-radius: 4px;
  padding: 8px 12px;
  color: #f9fafb;
  width: 200px;
  margin-right: 12px;
}

.single-config {
  background: #1f2937;
  border-radius: 8px;
  padding: 20px;
  display: none;
}

.strategy-control-card {
  background: #1a1a1a;
  border: 1px solid #2a2a2a;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 16px;
  transition: all 0.2s ease;
}

.strategy-control-card.active {
  border-color: #10b981;
  background: #1a2e1a;
}

.strategy-control-card.inactive {
  opacity: 0.7;
}

.strategy-control-card.underfunded {
  border-color: #dc2626;
  background: linear-gradient(135deg, #1f2937 0%, #2d1b1b 100%);
}

.strategy-control-card.underfunded .strategy-header {
  border-bottom-color: #dc2626;
}

.strategy-control-card.underfunded .allocation-badge {
  background: #dc2626;
  color: #ffffff;
}

.strategy-control-card.underfunded .strategy-status {
  border-left-color: #dc2626;
  background: #2d1b1b;
}

.underfunded-warning {
  background: #2d1b1b;
  border: 1px solid #dc2626;
  border-radius: 4px;
  padding: 12px;
  margin: 12px 0;
}

.underfunded-warning h5 {
  color: #fca5a5;
  font-size: 13px;
  font-weight: 600;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 6px;
}

.underfunded-warning h5::before {
  content: "⚠️";
  font-size: 14px;
}

.underfunded-warning p {
  color: #d1d5db;
  font-size: 12px;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.underfunded-warning .suggestion {
  color: #9ca3af;
  font-size: 11px;
  font-style: italic;
  margin: 0;
}

.strategy-control-card:hover {
  border-color: #3a3a3a;
}

.strategy-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  gap: 16px;
}

.strategy-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.strategy-title h4 {
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}

.strategy-toggle {
  margin-left: 16px;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #374151;
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #10b981;
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

.allocation-badge {
  background: #10b981;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.strategy-description {
  margin-bottom: 16px;
}

.strategy-description p {
  color: #9ca3af;
  font-size: 14px;
  margin: 0;
}

.strategy-status {
  margin-bottom: 16px;
  padding: 12px;
  background: #0f0f0f;
  border-radius: 4px;
  border-left: 3px solid #10b981;
}

.status-section h5 {
  color: #ffffff;
  font-size: 13px;
  font-weight: 500;
  margin: 0 0 12px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 8px;
  gap: 2px;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-label {
  color: #9ca3af;
  font-size: 11px;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-value {
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-value.positive {
  color: #10b981;
}

.status-value.neutral {
  color: #f59e0b;
}

.status-reason,
.status-next,
.status-trade {
  color: #e5e7eb;
  font-size: 12px;
  line-height: 1.4;
  font-style: italic;
}

.strategy-stats {
  border-top: 1px solid #2a2a2a;
  padding-top: 16px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.stat-row:last-child {
  margin-bottom: 0;
}

.stat-row span:first-child {
  color: #9ca3af;
  font-size: 13px;
}

.stat-row span:last-child {
  color: #ffffff;
  font-size: 13px;
  font-weight: 500;
}

.strategy-inactive {
  text-align: center;
  padding: 20px;
  color: #6b7280;
  font-style: italic;
}

.portfolio-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
  padding: 16px;
  background: #0f0f0f;
  border-radius: 6px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-item label {
  color: #9ca3af;
  font-size: 12px;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-item span {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .strategy-selector {
    flex-direction: column;
    align-items: stretch;
  }
  
  .strategy-selector select {
    min-width: auto;
  }
  
  .header h1 {
    font-size: 28px;
  }
}
