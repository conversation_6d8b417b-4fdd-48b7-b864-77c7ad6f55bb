import { BaseStrategy } from './BaseStrategy.js';
import { TrendFollowingStrategy } from './TrendFollowingStrategy.js';
import { SimpleStrategy } from './SimpleStrategy.js';
import { VolatilityWeightedStrategy } from './VolatilityWeightedStrategy.js';
import { MovingAverageCrossoverStrategy } from './MovingAverageCrossoverStrategy.js';
import { MeanReversionStrategy } from './MeanReversionStrategy.js';
import { RangeTradingStrategy } from './RangeTradingStrategy.js';
import { BreakoutStrategy } from './BreakoutStrategy.js';
import { TrailingEntryStrategy } from './TrailingEntryStrategy.js';
import { BuyTheDipStrategy } from './BuyTheDipStrategy.js';
import { PortfolioManager } from './PortfolioManager.js';

// Re-export for external use
export { 
  BaseStrategy, 
  SimpleStrategy, 
  TrendFollowingStrategy, 
  VolatilityWeightedStrategy, 
  MovingAverageCrossoverStrategy, 
  MeanReversionStrategy, 
  RangeTradingStrategy, 
  BreakoutStrategy, 
  TrailingEntryStrategy, 
  BuyTheDipStrategy,
  PortfolioManager
};

// Strategy registry for easy access
export const STRATEGIES = {
  'trend-following': TrendFollowingStrategy,
  'simple': SimpleStrategy,
  'volatility-weighted': VolatilityWeightedStrategy,
  'moving-average-crossover': MovingAverageCrossoverStrategy,
  'mean-reversion': MeanReversionStrategy,
  'range-trading': RangeTradingStrategy,
  'breakout': BreakoutStrategy,
  'trailing-entry': TrailingEntryStrategy,
  'buy-the-dip': BuyTheDipStrategy,
  'portfolio-manager': PortfolioManager
};

export function createStrategy(strategyName, config = {}) {
  const StrategyClass = STRATEGIES[strategyName];
  if (!StrategyClass) {
    throw new Error(`Unknown strategy: ${strategyName}. Available: ${Object.keys(STRATEGIES).join(', ')}`);
  }
  return new StrategyClass(config);
}


