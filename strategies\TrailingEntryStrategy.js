import { BaseStrategy } from './BaseStrategy.js';

export class TrailingEntryStrategy extends BaseStrategy {
  constructor(config = {}) {
    super({
      trailingLookback: 10,
      recoveryPercent: 0.03, // 3% recovery from local minimum
      ...config
    });
  }

  shouldEnterTrade(currentPrice, marketData) {
    // Don't enter if we already have a position
    if (this.hasPosition(marketData)) {
      const position = marketData.currentPosition;
      return {
        shouldEnter: false,
        reason: `Already holding ${position.quantity.toFixed(8)} BTC at avg price $${position.averagePrice.toFixed(2)}`
      };
    }

    const { trailingLookback, recoveryPercent } = this.config;

    // Ensure we have enough history to determine a local minimum
    if (this.priceHistory.length < trailingLookback) {
      return {
        shouldEnter: false,
        reason: `Accumulating price data (${this.priceHistory.length}/${trailingLookback} required)`
      };
    }

    const recentPrices = this.priceHistory.slice(-trailingLookback);
    const localLow = Math.min(...recentPrices);
    const recoveryTarget = localLow * (1 + recoveryPercent);
    const hasRecovered = currentPrice >= recoveryTarget;
    const currentRecovery = ((currentPrice - localLow) / localLow * 100);
    const neededRecovery = (recoveryPercent * 100);

    if (hasRecovered) {
      return {
        shouldEnter: true,
        reason: `Recovery confirmed: price $${currentPrice.toFixed(2)} recovered ${currentRecovery.toFixed(1)}% from local low $${localLow.toFixed(2)} (target: ${neededRecovery.toFixed(1)}%)`
      };
    } else {
      const distanceToTarget = ((recoveryTarget - currentPrice) / currentPrice * 100);
      return {
        shouldEnter: false,
        reason: `Waiting for ${neededRecovery.toFixed(1)}% recovery from low $${localLow.toFixed(2)} (target: $${recoveryTarget.toFixed(2)}, current: ${currentRecovery.toFixed(1)}%, ${distanceToTarget.toFixed(1)}% to go)`
      };
    }
  }

  calculateStopLoss(currentPrice) {
    const stopPercent = this.calculateATR(this.priceHistory);
    return {
      stopPercent,
      stopPrice: currentPrice * (1 - stopPercent),
      reason: `ATR-based stop at ${Math.round(stopPercent * 10000) / 100}%`
    };
  }

  getPositionSize(availableCash, currentPrice) {
    return super.getPositionSize(availableCash, currentPrice);
  }

  getDescription() {
    return 'Enters after a pullback: waits for price to rebound a specified percentage from a local minimum. Useful for capturing reversal moves.';
  }

  getConfigSchema() {
    return {
      ...super.getConfigSchema(),
      trailingLookback: {
        type: 'number',
        label: 'Trailing Lookback',
        min: 5,
        max: 50,
        default: 10,
        description: 'Number of periods to determine the local minimum'
      },
      recoveryPercent: {
        type: 'number',
        label: 'Recovery %',
        min: 0.01,
        max: 0.1,
        step: 0.005,
        default: 0.03,
        description: 'Percentage recovery from the local low required to trigger entry'
      }
    };
  }
}
