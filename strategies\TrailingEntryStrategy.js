import { BaseStrategy } from './BaseStrategy.js';

export class TrailingEntryStrategy extends BaseStrategy {
  constructor(config = {}) {
    super({
      trailingLookback: 10,
      recoveryPercent: 0.03, // 3% recovery from local minimum
      ...config
    });
  }

  shouldEnterTrade(currentPrice) {
    const { trailingLookback, recoveryPercent } = this.config;
    
    // Ensure we have enough history to determine a local minimum
    if (this.priceHistory.length < trailingLookback) {
      return { shouldEnter: false };
    }
    
    const recentPrices = this.priceHistory.slice(-trailingLookback);
    const localLow = Math.min(...recentPrices);
    
    const hasRecovered = currentPrice >= localLow * (1 + recoveryPercent);
    
    return {
      shouldEnter: hasRecovered,
      reason: hasRecovered
        ? `Price recovered ${Math.round(recoveryPercent * 100)}% from local low of ${localLow.toFixed(2)}`
        : `Waiting for a ${Math.round(recoveryPercent * 100)}% recovery from local low (${localLow.toFixed(2)})`
    };
  }

  calculateStopLoss(currentPrice) {
    const stopPercent = this.calculateATR(this.priceHistory);
    return {
      stopPercent,
      stopPrice: currentPrice * (1 - stopPercent),
      reason: `ATR-based stop at ${Math.round(stopPercent * 10000) / 100}%`
    };
  }

  getPositionSize(availableCash, currentPrice) {
    return super.getPositionSize(availableCash, currentPrice);
  }

  getDescription() {
    return 'Enters after a pullback: waits for price to rebound a specified percentage from a local minimum. Useful for capturing reversal moves.';
  }

  getConfigSchema() {
    return {
      ...super.getConfigSchema(),
      trailingLookback: {
        type: 'number',
        label: 'Trailing Lookback',
        min: 5,
        max: 50,
        default: 10,
        description: 'Number of periods to determine the local minimum'
      },
      recoveryPercent: {
        type: 'number',
        label: 'Recovery %',
        min: 0.01,
        max: 0.1,
        step: 0.005,
        default: 0.03,
        description: 'Percentage recovery from the local low required to trigger entry'
      }
    };
  }
}
