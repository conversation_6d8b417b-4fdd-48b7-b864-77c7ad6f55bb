import { BaseStrategy } from './BaseStrategy.js';

export class MeanReversionStrategy extends BaseStrategy {
  constructor(config = {}) {
    super({
      meanPeriod: 20,
      deviationThreshold: 0.04, // 4% below SMA
      ...config
    });
  }

  shouldEnterTrade(currentPrice) {
    const { meanPeriod, deviationThreshold } = this.config;

    if (this.priceHistory.length < meanPeriod) {
      return { shouldEnter: false };
    }

    const sma = this.calculateSMA(this.priceHistory, meanPeriod);
    const deviation = (sma - currentPrice) / sma;

    const signal = deviation >= deviationThreshold;

    return {
      shouldEnter: signal,
      reason: signal
        ? `Price ${Math.round(deviation * 100)}% below mean`
        : 'Price not far enough below mean'
    };
  }

  calculateStopLoss(currentPrice) {
    const stopPercent = this.calculateATR(this.priceHistory);
    return {
      stopPercent,
      stopPrice: currentPrice * (1 - stopPercent),
      reason: `ATR-based stop at ${Math.round(stopPercent * 10000) / 100}%`
    };
  }

  getPositionSize(availableCash, currentPrice) {
    return super.getPositionSize(availableCash, currentPrice);
  }

  getDescription() {
    return 'Buys when price deviates significantly below the moving average. Suitable for ranging markets.';
  }

  getConfigSchema() {
    return {
      ...super.getConfigSchema(),
      meanPeriod: {
        type: 'number',
        label: 'SMA Period',
        min: 5,
        max: 100,
        default: 20,
        description: 'Period used to calculate mean (SMA)'
      },
      deviationThreshold: {
        type: 'number',
        label: 'Deviation Threshold (%)',
        min: 0.01,
        max: 0.1,
        step: 0.005,
        default: 0.04,
        description: 'How far below mean price must be to trigger entry (as a percent)'
      }
    };
  }
}
