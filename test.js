import base64js from 'base64-js';
import nacl from 'tweetnacl';

// Add this test function to verify signature generation
function testSignature() {
  // Test values from documentation - exact format
  const testApiKey = "rh-****************************************";
  const testTimestamp = "1698708981";
  const testPath = "/api/v1/crypto/trading/orders/";
  const testMethod = "POST";
  
  // Try to mimic Python's str() representation of a dict
  const testBodyDict = {
    "client_order_id": "131de903-5a9c-4260-abc1-28d562a5dcf0",
    "side": "buy",
    "symbol": "BTC-USD",
    "type": "market",
    "market_order_config": {
      "asset_quantity": "0.1"
    }
  };
  
  // Python str() on dict gives something like: {'key': 'value', ...}
  const pythonDictStr = `{'client_order_id': '131de903-5a9c-4260-abc1-28d562a5dcf0', 'side': 'buy', 'symbol': 'BTC-USD', 'type': 'market', 'market_order_config': {'asset_quantity': '0.1'}}`;
  
  console.log('Trying Python dict str representation...');
  const pythonMessage = `${testApiKey}${testTimestamp}${testPath}${testMethod}${pythonDictStr}`;
  console.log('Python dict message:', pythonMessage);
  
  const expectedSignature = "q/nEtxp/P2Or3hph3KejBqnw5o9qeuQ+hYRnB56FaHbjDsNUY9KhB1asMxohDnzdVFSD7StaTqjSd9U9HvaRAw==";
  const testPrivateKey = base64js.toByteArray("xQnTJVeQLmw1/Mg2YimEViSpw/SdJcgNXZ5kQkAXNPU=");
  const testKeyPair = nacl.sign.keyPair.fromSeed(testPrivateKey);
  
  const encodedMessage = new TextEncoder().encode(pythonMessage);
  const signature = nacl.sign.detached(encodedMessage, testKeyPair.secretKey);
  const base64Signature = base64js.fromByteArray(signature);
  
  console.log('Generated signature:', base64Signature);
  console.log('Matches expected:', base64Signature === expectedSignature);
  
  // Let's also try without any body at all (maybe the example is wrong)
  console.log('\nTrying without body:');
  const noBodyMessage = `${testApiKey}${testTimestamp}${testPath}${testMethod}`;
  console.log('No body message:', noBodyMessage);
  
  const noBodyEncoded = new TextEncoder().encode(noBodyMessage);
  const noBodySig = nacl.sign.detached(noBodyEncoded, testKeyPair.secretKey);
  const noBodyBase64 = base64js.fromByteArray(noBodySig);
  
  console.log('No body signature:', noBodyBase64);
  console.log('No body matches expected:', noBodyBase64 === expectedSignature);
}

// Call test on startup
testSignature();
