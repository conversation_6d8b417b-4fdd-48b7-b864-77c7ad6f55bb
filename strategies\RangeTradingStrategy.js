import { BaseStrategy } from './BaseStrategy.js';

export class RangeTradingStrategy extends BaseStrategy {
  constructor(config = {}) {
    super({
      rangeLookback: 20,
      entryBufferPercent: 0.02, // within 2% of range low
      ...config
    });
  }

  shouldEnterTrade(currentPrice) {
    const { rangeLookback, entryBufferPercent } = this.config;

    if (this.priceHistory.length < rangeLookback) {
      return { shouldEnter: false };
    }

    const recentPrices = this.priceHistory.slice(-rangeLookback);
    const rangeHigh = Math.max(...recentPrices);
    const rangeLow = Math.min(...recentPrices);

    const buffer = (rangeHigh - rangeLow) * entryBufferPercent;
    const nearSupport = currentPrice <= rangeLow + buffer;

    // Optional: avoid trading if we're in a strong trend
    const isTrending = !this.isUptrend(recentPrices, Math.floor(rangeLookback / 2)) &&
                       !this.isUptrend(recentPrices.map(p => -p), Math.floor(rangeLookback / 2)); // crude downtrend check

    return {
      shouldEnter: nearSupport && !isTrending,
      reason: nearSupport && !isTrending
        ? `Price near support (${rangeLow.toFixed(2)}) in a range`
        : 'Not in range or price too high'
    };
  }

  calculateStopLoss(currentPrice) {
    const stopPercent = this.calculateATR(this.priceHistory);
    return {
      stopPercent,
      stopPrice: currentPrice * (1 - stopPercent),
      reason: `ATR-based stop at ${Math.round(stopPercent * 10000) / 100}%`
    };
  }

  getPositionSize(availableCash, currentPrice) {
    return super.getPositionSize(availableCash, currentPrice);
  }

  getDescription() {
    return 'Buys near support levels in sideways markets. Best suited for choppy or consolidating markets.';
  }

  getConfigSchema() {
    return {
      ...super.getConfigSchema(),
      rangeLookback: {
        type: 'number',
        label: 'Range Lookback',
        min: 10,
        max: 100,
        default: 20,
        description: 'Period used to calculate support and resistance range'
      },
      entryBufferPercent: {
        type: 'number',
        label: 'Entry Buffer %',
        min: 0.005,
        max: 0.05,
        step: 0.005,
        default: 0.02,
        description: 'How close to support price must be to enter (as % of range width)'
      }
    };
  }
}
