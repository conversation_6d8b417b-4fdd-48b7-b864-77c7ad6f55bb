import { BaseStrategy } from './BaseStrategy.js';

export class RangeTradingStrategy extends BaseStrategy {
  constructor(config = {}) {
    super({
      rangeLookback: 20,
      entryBufferPercent: 0.02, // within 2% of range low
      ...config
    });
  }

  shouldEnterTrade(currentPrice, marketData) {
    // Don't enter if we already have a position
    if (this.hasPosition(marketData)) {
      const position = marketData.currentPosition;
      return {
        shouldEnter: false,
        reason: `Already holding ${position.quantity.toFixed(8)} BTC at avg price $${position.averagePrice.toFixed(2)}`
      };
    }

    const { rangeLookback, entryBufferPercent } = this.config;

    if (this.priceHistory.length < rangeLookback) {
      return {
        shouldEnter: false,
        reason: `Accumulating price data (${this.priceHistory.length}/${rangeLookback} required)`
      };
    }

    const recentPrices = this.priceHistory.slice(-rangeLookback);
    const rangeHigh = Math.max(...recentPrices);
    const rangeLow = Math.min(...recentPrices);
    const rangeSize = rangeHigh - rangeLow;

    const buffer = rangeSize * entryBufferPercent;
    const supportLevel = rangeLow + buffer;
    const nearSupport = currentPrice <= supportLevel;

    // Check if we're in a ranging market (not trending)
    const trendLookback = Math.floor(rangeLookback / 2);
    const isUptrending = this.isUptrend(recentPrices, trendLookback);
    const isDowntrending = this.isUptrend(recentPrices.map(p => -p), trendLookback);
    const isRanging = !isUptrending && !isDowntrending;

    const distanceFromSupport = ((currentPrice - rangeLow) / rangeSize * 100);

    if (nearSupport && isRanging) {
      return {
        shouldEnter: true,
        reason: `Range trading: price $${currentPrice.toFixed(2)} near support $${rangeLow.toFixed(2)} (${distanceFromSupport.toFixed(1)}% from bottom, range: $${rangeLow.toFixed(2)}-$${rangeHigh.toFixed(2)})`
      };
    } else if (!nearSupport && isRanging) {
      return {
        shouldEnter: false,
        reason: `In range but too far from support: $${currentPrice.toFixed(2)} vs support $${supportLevel.toFixed(2)} (${distanceFromSupport.toFixed(1)}% from bottom)`
      };
    } else if (isUptrending) {
      return {
        shouldEnter: false,
        reason: `Market uptrending, not suitable for range trading (range: $${rangeLow.toFixed(2)}-$${rangeHigh.toFixed(2)})`
      };
    } else if (isDowntrending) {
      return {
        shouldEnter: false,
        reason: `Market downtrending, not suitable for range trading (range: $${rangeLow.toFixed(2)}-$${rangeHigh.toFixed(2)})`
      };
    } else {
      return {
        shouldEnter: false,
        reason: `Monitoring range: support $${rangeLow.toFixed(2)}, resistance $${rangeHigh.toFixed(2)}, current $${currentPrice.toFixed(2)}`
      };
    }
  }

  calculateStopLoss(currentPrice) {
    const stopPercent = this.calculateATR(this.priceHistory);
    return {
      stopPercent,
      stopPrice: currentPrice * (1 - stopPercent),
      reason: `ATR-based stop at ${Math.round(stopPercent * 10000) / 100}%`
    };
  }

  getPositionSize(availableCash, currentPrice) {
    return super.getPositionSize(availableCash, currentPrice);
  }

  getDescription() {
    return 'Buys near support levels in sideways markets. Best suited for choppy or consolidating markets.';
  }

  getConfigSchema() {
    return {
      ...super.getConfigSchema(),
      rangeLookback: {
        type: 'number',
        label: 'Range Lookback',
        min: 10,
        max: 100,
        default: 20,
        description: 'Period used to calculate support and resistance range'
      },
      entryBufferPercent: {
        type: 'number',
        label: 'Entry Buffer %',
        min: 0.005,
        max: 0.05,
        step: 0.005,
        default: 0.02,
        description: 'How close to support price must be to enter (as % of range width)'
      }
    };
  }
}
