{"_from": "iconv-lite@0.4.24", "_id": "iconv-lite@0.4.24", "_inBundle": false, "_integrity": "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==", "_location": "/iconv-lite", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "iconv-lite@0.4.24", "name": "iconv-lite", "escapedName": "iconv-lite", "rawSpec": "0.4.24", "saveSpec": null, "fetchSpec": "0.4.24"}, "_requiredBy": ["/body-parser", "/raw-body"], "_resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz", "_shasum": "2022b4b25fbddc21d2f524974a474aafe733908b", "_spec": "iconv-lite@0.4.24", "_where": "D:\\Projects\\RHCrypto\\node_modules\\body-parser", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "browser": {"./lib/extend-node": false, "./lib/streams": false}, "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "bundleDependencies": false, "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "deprecated": false, "description": "Convert character encodings in pure javascript.", "devDependencies": {"async": "*", "errto": "*", "iconv": "*", "istanbul": "*", "mocha": "^3.1.0", "request": "~2.87.0", "semver": "*", "unorm": "*"}, "engines": {"node": ">=0.10.0"}, "homepage": "https://github.com/ashtuchkin/iconv-lite", "keywords": ["iconv", "convert", "charset", "icu"], "license": "MIT", "main": "./lib/index.js", "name": "iconv-lite", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "typings": "./lib/index.d.ts", "version": "0.4.24"}