// Time-based candle aggregation system for proper technical analysis
export class CandleAggregator {
  constructor(timeframe = '5m') {
    this.timeframe = timeframe;
    this.timeframeMs = this.parseTimeframe(timeframe);
    this.currentCandle = null;
    this.completedCandles = [];
    this.maxCandles = 200; // Keep last 200 candles
  }

  // Parse timeframe string to milliseconds
  parseTimeframe(timeframe) {
    const unit = timeframe.slice(-1);
    const value = parseInt(timeframe.slice(0, -1));
    
    switch (unit) {
      case 'm': return value * 60 * 1000;
      case 'h': return value * 60 * 60 * 1000;
      case 'd': return value * 24 * 60 * 60 * 1000;
      default: throw new Error(`Invalid timeframe: ${timeframe}`);
    }
  }

  // Get the start time for a candle containing the given timestamp
  getCandleStartTime(timestamp) {
    return Math.floor(timestamp / this.timeframeMs) * this.timeframeMs;
  }

  // Add a new price update
  addPrice(price, timestamp = Date.now()) {
    const candleStartTime = this.getCandleStartTime(timestamp);
    
    // If this is a new candle period
    if (!this.currentCandle || this.currentCandle.startTime !== candleStartTime) {
      // Complete the previous candle if it exists
      if (this.currentCandle) {
        this.completedCandles.push({ ...this.currentCandle });
        
        // Maintain max candles limit
        if (this.completedCandles.length > this.maxCandles) {
          this.completedCandles = this.completedCandles.slice(-this.maxCandles);
        }
      }
      
      // Start a new candle
      this.currentCandle = {
        startTime: candleStartTime,
        endTime: candleStartTime + this.timeframeMs,
        open: price,
        high: price,
        low: price,
        close: price,
        volume: 1,
        priceUpdates: 1
      };
    } else {
      // Update the current candle
      this.currentCandle.high = Math.max(this.currentCandle.high, price);
      this.currentCandle.low = Math.min(this.currentCandle.low, price);
      this.currentCandle.close = price;
      this.currentCandle.volume += 1;
      this.currentCandle.priceUpdates += 1;
    }
  }

  // Get all completed candles (not including current incomplete candle)
  getAllCandles() {
    return [...this.completedCandles];
  }

  // Get all candles including current incomplete candle
  getAllCandlesIncludingCurrent() {
    const candles = [...this.completedCandles];
    if (this.currentCandle) {
      candles.push({ ...this.currentCandle });
    }
    return candles;
  }

  // Get the latest N completed candles
  getLatestCandles(count) {
    return this.completedCandles.slice(-count);
  }

  // Get current price (close of current candle or last completed candle)
  getCurrentPrice() {
    if (this.currentCandle) {
      return this.currentCandle.close;
    }
    if (this.completedCandles.length > 0) {
      return this.completedCandles[this.completedCandles.length - 1].close;
    }
    return null;
  }

  // Check if we have enough candles for analysis
  hasEnoughCandles(periods) {
    return this.completedCandles.length >= periods;
  }

  // Get closing prices for the last N periods
  getClosingPrices(periods = null) {
    const candles = periods ? this.getLatestCandles(periods) : this.completedCandles;
    return candles.map(candle => candle.close);
  }

  // Calculate Simple Moving Average
  calculateSMA(periods) {
    if (!this.hasEnoughCandles(periods)) {
      return null;
    }
    
    const prices = this.getClosingPrices(periods);
    const sum = prices.reduce((acc, price) => acc + price, 0);
    return sum / periods;
  }

  // Get highest high over N periods
  getHighestHigh(periods) {
    if (!this.hasEnoughCandles(periods)) {
      return null;
    }
    
    const candles = this.getLatestCandles(periods);
    return Math.max(...candles.map(candle => candle.high));
  }

  // Get lowest low over N periods
  getLowestLow(periods) {
    if (!this.hasEnoughCandles(periods)) {
      return null;
    }
    
    const candles = this.getLatestCandles(periods);
    return Math.min(...candles.map(candle => candle.low));
  }

  // Calculate Average True Range (ATR)
  calculateATR(periods = 14) {
    if (!this.hasEnoughCandles(periods + 1)) {
      return null;
    }
    
    const candles = this.getLatestCandles(periods + 1);
    const trueRanges = [];
    
    for (let i = 1; i < candles.length; i++) {
      const current = candles[i];
      const previous = candles[i - 1];
      
      const tr1 = current.high - current.low;
      const tr2 = Math.abs(current.high - previous.close);
      const tr3 = Math.abs(current.low - previous.close);
      
      trueRanges.push(Math.max(tr1, tr2, tr3));
    }
    
    return trueRanges.reduce((acc, tr) => acc + tr, 0) / trueRanges.length;
  }

  // Get candle statistics
  getStats() {
    return {
      timeframe: this.timeframe,
      timeframeMs: this.timeframeMs,
      completedCandles: this.completedCandles.length,
      currentCandle: this.currentCandle ? {
        startTime: this.currentCandle.startTime,
        priceUpdates: this.currentCandle.priceUpdates,
        ohlc: {
          open: this.currentCandle.open,
          high: this.currentCandle.high,
          low: this.currentCandle.low,
          close: this.currentCandle.close
        }
      } : null,
      latestCompletedCandle: this.completedCandles.length > 0 ? 
        this.completedCandles[this.completedCandles.length - 1] : null
    };
  }

  // Get timeframe description for display
  getTimeframeDescription() {
    const unit = this.timeframe.slice(-1);
    const value = this.timeframe.slice(0, -1);
    
    switch (unit) {
      case 'm': return `${value}-minute`;
      case 'h': return `${value}-hour`;
      case 'd': return `${value}-day`;
      default: return this.timeframe;
    }
  }

  // Clear all candle data
  clear() {
    this.currentCandle = null;
    this.completedCandles = [];
  }

  // Export candles in a format suitable for charting libraries
  exportForChart() {
    return this.completedCandles.map(candle => ({
      time: candle.startTime,
      open: candle.open,
      high: candle.high,
      low: candle.low,
      close: candle.close,
      volume: candle.volume
    }));
  }

  // Get candles within a specific time range
  getCandlesInRange(startTime, endTime) {
    return this.completedCandles.filter(candle => 
      candle.startTime >= startTime && candle.startTime < endTime
    );
  }

  // Get the most recent complete candle
  getLatestCompleteCandle() {
    return this.completedCandles.length > 0 ? 
      this.completedCandles[this.completedCandles.length - 1] : null;
  }

  // Check if current candle is near completion (for timing trades)
  isCurrentCandleNearCompletion(thresholdPercent = 0.9) {
    if (!this.currentCandle) return false;
    
    const elapsed = Date.now() - this.currentCandle.startTime;
    const progress = elapsed / this.timeframeMs;
    return progress >= thresholdPercent;
  }

  // Get price change percentage for the current candle
  getCurrentCandleChange() {
    if (!this.currentCandle) return 0;
    
    const change = this.currentCandle.close - this.currentCandle.open;
    return (change / this.currentCandle.open) * 100;
  }

  // Get volatility measure for recent candles
  getRecentVolatility(periods = 20) {
    if (!this.hasEnoughCandles(periods)) {
      return null;
    }
    
    const candles = this.getLatestCandles(periods);
    const changes = candles.map(candle => 
      Math.abs((candle.close - candle.open) / candle.open)
    );
    
    return changes.reduce((acc, change) => acc + change, 0) / changes.length;
  }

  // Detect if we're in a new candle period
  isNewCandlePeriod(timestamp = Date.now()) {
    const candleStartTime = this.getCandleStartTime(timestamp);
    return !this.currentCandle || this.currentCandle.startTime !== candleStartTime;
  }
}
