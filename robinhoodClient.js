// robinhoodClient.js
import axios from 'axios';
import nacl from 'tweetnacl';
import base64js from 'base64-js';
import dotenv from 'dotenv';
dotenv.config();

const API_KEY = process.env.ROBINHOOD_API_KEY;
const BASE64_PRIVATE_KEY = process.env.ROBINHOOD_PRIVATE_KEY;
const BASE_URL = 'https://trading.robinhood.com';

// Validate environment variables
if (!API_KEY) {
  throw new Error('ROBINHOOD_API_KEY environment variable is required');
}

if (!BASE64_PRIVATE_KEY) {
  throw new Error('ROBINHOOD_PRIVATE_KEY environment variable is required');
}

console.log('API Key loaded:', API_KEY ? `${API_KEY.substring(0, 8)}...` : 'MISSING');
console.log('API Key length:', API_KEY ? API_KEY.length : 0);
console.log('Private Key loaded:', BASE64_PRIVATE_KEY ? 'YES' : 'NO');
console.log('Private Key length:', BASE64_PRIVATE_KEY ? BASE64_PRIVATE_KEY.length : 0);

const privateKeyBytes = base64js.toByteArray(BASE64_PRIVATE_KEY);

if (privateKeyBytes.length !== 32) {
  throw new Error(`Invalid private key length: ${privateKeyBytes.length}. Must be 32 bytes.`);
}

const keyPair = nacl.sign.keyPair.fromSeed(privateKeyBytes);
console.log('Key pair generated successfully');

function getTimestamp() {
  // Subtract more seconds to account for clock drift and network latency
  return Math.floor(Date.now() / 1000) - 30;
}

function signRequest(method, path, body, timestamp) {
  let bodyStr = '';
  
  if (body) {
    if (typeof body === 'string') {
      // Parse JSON string and convert to Python dict format
      const bodyObj = JSON.parse(body);
      bodyStr = convertToPythonDictStr(bodyObj);
    } else if (typeof body === 'object') {
      // Convert object to Python dict format
      bodyStr = convertToPythonDictStr(body);
    }
  }
  
  const message = `${API_KEY}${timestamp}${path}${method}${bodyStr}`;
  const encodedMessage = new TextEncoder().encode(message);
  const signature = nacl.sign.detached(encodedMessage, keyPair.secretKey);
  return base64js.fromByteArray(signature);
}

function convertToPythonDictStr(obj) {
  if (obj === null) return 'None';
  if (typeof obj === 'string') return `'${obj}'`;
  if (typeof obj === 'number' || typeof obj === 'boolean') return obj.toString();
  if (Array.isArray(obj)) {
    return `[${obj.map(convertToPythonDictStr).join(', ')}]`;
  }
  if (typeof obj === 'object') {
    const pairs = Object.entries(obj).map(([key, value]) => 
      `'${key}': ${convertToPythonDictStr(value)}`
    );
    return `{${pairs.join(', ')}}`;
  }
  return obj.toString();
}

// Rate limiting - simple implementation
let lastRequestTime = 0;
const MIN_REQUEST_INTERVAL = 1000; // 1 second between requests

async function makeApiRequest(method, path, body = '') {
  // Rate limiting
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;
  if (timeSinceLastRequest < MIN_REQUEST_INTERVAL) {
    const waitTime = MIN_REQUEST_INTERVAL - timeSinceLastRequest;
    await new Promise(resolve => setTimeout(resolve, waitTime));
  }
  lastRequestTime = Date.now();

  const timestamp = getTimestamp();
  const signature = signRequest(method, path, body, timestamp);

  const headers = {
    'x-api-key': API_KEY,
    'x-signature': signature,
    'x-timestamp': timestamp.toString(),
    'Content-Type': 'application/json'
  };

  const url = BASE_URL + path;

  try {
    const config = {
      method,
      url,
      headers,
      timeout: 15000
    };
    
    if (body && method !== 'GET') {
      config.data = typeof body === 'string' ? JSON.parse(body) : body;
    }
    
    const res = await axios(config);
    return res.data;
  } catch (e) {
    console.error(`[Robinhood API Error] ${e.message}`);
    if (e.response) {
      console.error('Status:', e.response.status);
      console.error('Data:', e.response.data);
    }
    
    if (e.response?.data?.includes?.('Timestamp') && !e.retried) {
      await new Promise(resolve => setTimeout(resolve, 2000));
      e.retried = true;
      return makeApiRequest(method, path, body);
    }
    
    return null;
  }
}

export const RobinhoodAPI = {
  getAccount: () => makeApiRequest('GET', '/api/v1/crypto/trading/accounts/'),
  getHoldings: () => makeApiRequest('GET', '/api/v1/crypto/trading/holdings/'),
  getOrders: () => makeApiRequest('GET', '/api/v1/crypto/trading/orders/'),
  getOrder: (id) => makeApiRequest('GET', `/api/v1/crypto/trading/orders/${id}/`),
  cancelOrder: (id) => makeApiRequest('POST', `/api/v1/crypto/trading/orders/${id}/cancel/`),
  getBestBidAsk: (symbol) => makeApiRequest('GET', `/api/v1/crypto/marketdata/best_bid_ask/?symbol=${symbol}`),
  getEstimatedPrice: (symbol, side, quantity) =>
    makeApiRequest('GET', `/api/v1/crypto/marketdata/estimated_price/?symbol=${symbol}&side=${side}&quantity=${quantity}`),
  placeOrder: (clientOrderId, side, type, symbol, orderConfig) =>
    makeApiRequest('POST', '/api/v1/crypto/trading/orders/', JSON.stringify({
      client_order_id: clientOrderId,
      side,
      type,
      symbol,
      [`${type}_order_config`]: orderConfig
    })),
  // Add a method to get historical quotes
  getHistoricalOrders: (symbol = 'BTC-USD', limit = 100) => 
    makeApiRequest('GET', `/api/v1/crypto/trading/orders/?symbol=${symbol}&limit=${limit}`),
  // Get orders within a specific time range
  getOrdersInTimeRange: (symbol = 'BTC-USD', startTime, endTime, limit = 100) => 
    makeApiRequest('GET', `/api/v1/crypto/trading/orders/?symbol=${symbol}&created_at_start=${startTime}&created_at_end=${endTime}&limit=${limit}`)
};
