// Config validation and migration utility
import { createStrategy } from './strategies/index.js';
import fs from 'fs';
import path from 'path';

export class ConfigValidator {
  constructor() {
    this.validationErrors = [];
    this.fixesApplied = [];
    this.migrationLog = [];
  }

  // Get default configuration for a strategy
  getStrategyDefaults(strategyName) {
    try {
      const tempStrategy = createStrategy(strategyName, {});
      const schema = tempStrategy.getConfigSchema ? tempStrategy.getConfigSchema() : {};
      const defaults = {};
      
      // Extract default values from schema
      Object.entries(schema).forEach(([key, config]) => {
        if (config.default !== undefined) {
          defaults[key] = config.default;
        }
      });
      
      // Add strategy-specific required defaults
      const strategyDefaults = this.getStrategySpecificDefaults(strategyName);
      return { ...defaults, ...strategyDefaults };
    } catch (error) {
      console.warn(`Could not get defaults for strategy ${strategyName}:`, error.message);
      return {};
    }
  }

  // Strategy-specific defaults that might not be in schema
  getStrategySpecificDefaults(strategyName) {
    const defaults = {
      'trend-following': {
        minTrendLookback: 20,
        shortTermPeriod: 5,
        recentHighThreshold: 0.98,
        volatilityLookback: 14
      },
      'buy-the-dip': {
        minTrendLookback: 20,
        dipThreshold: 0.05,
        dipLookback: 5
      },
      'volatility-weighted': {
        minTrendLookback: 20,
        riskFactor: 0.02
      },
      'moving-average-crossover': {
        shortPeriod: 5,
        longPeriod: 20
      },
      'mean-reversion': {
        meanPeriod: 20,
        deviationThreshold: 0.04
      },
      'range-trading': {
        rangeLookback: 20,
        entryBufferPercent: 0.02
      },
      'breakout': {
        breakoutLookback: 20,
        confirmRetest: false
      },
      'trailing-entry': {
        trailingLookback: 10,
        recoveryPercent: 0.03
      },
      'simple': {
        stopLossPercent: 0.025
      }
    };
    
    return defaults[strategyName] || {};
  }

  // Validate and fix a single strategy configuration
  validateStrategyConfig(strategyConfig) {
    const { name, allocation, config } = strategyConfig;
    const fixes = [];
    
    // Validate strategy name
    if (!name || typeof name !== 'string') {
      this.validationErrors.push('Strategy missing valid name');
      return null;
    }

    // Validate allocation
    let validAllocation = allocation;
    if (typeof allocation !== 'number' || allocation <= 0 || allocation > 1) {
      validAllocation = 0.1; // Default 10%
      fixes.push(`Fixed invalid allocation: ${allocation} → ${validAllocation}`);
    }

    // Get required defaults for this strategy
    const requiredDefaults = this.getStrategyDefaults(name);
    
    // Merge config with defaults, preserving existing values
    const validConfig = { ...requiredDefaults, ...config };
    
    // Check for missing required parameters
    Object.entries(requiredDefaults).forEach(([key, defaultValue]) => {
      if (config[key] === undefined) {
        fixes.push(`Added missing parameter: ${key} = ${defaultValue}`);
      }
    });

    // Validate specific parameter ranges
    const parameterFixes = this.validateParameterRanges(name, validConfig);
    fixes.push(...parameterFixes);

    if (fixes.length > 0) {
      this.fixesApplied.push({
        strategy: name,
        fixes: fixes
      });
    }

    return {
      name,
      allocation: validAllocation,
      config: validConfig
    };
  }

  // Validate parameter ranges for specific strategies
  validateParameterRanges(strategyName, config) {
    const fixes = [];
    
    // Common validations
    if (config.accountPercentToUse !== undefined) {
      if (config.accountPercentToUse <= 0 || config.accountPercentToUse > 1) {
        config.accountPercentToUse = 0.5;
        fixes.push('Fixed accountPercentToUse to valid range (0-1)');
      }
    }

    // Strategy-specific validations
    switch (strategyName) {
      case 'trend-following':
        if (config.minTrendLookback < 5 || config.minTrendLookback > 100) {
          config.minTrendLookback = 20;
          fixes.push('Fixed minTrendLookback to valid range (5-100)');
        }
        if (config.recentHighThreshold < 0.9 || config.recentHighThreshold > 1) {
          config.recentHighThreshold = 0.98;
          fixes.push('Fixed recentHighThreshold to valid range (0.9-1.0)');
        }
        break;
        
      case 'buy-the-dip':
        if (config.dipThreshold < 0.01 || config.dipThreshold > 0.2) {
          config.dipThreshold = 0.05;
          fixes.push('Fixed dipThreshold to valid range (0.01-0.2)');
        }
        if (config.dipLookback < 2 || config.dipLookback > 20) {
          config.dipLookback = 5;
          fixes.push('Fixed dipLookback to valid range (2-20)');
        }
        break;
        
      case 'mean-reversion':
        if (config.deviationThreshold < 0.01 || config.deviationThreshold > 0.1) {
          config.deviationThreshold = 0.04;
          fixes.push('Fixed deviationThreshold to valid range (0.01-0.1)');
        }
        break;
    }
    
    return fixes;
  }

  // Validate entire portfolio configuration
  validatePortfolioConfig(config) {
    if (!config.portfolio) {
      config.portfolio = {
        strategies: [],
        rebalanceFrequency: 86400000,
        performanceWindow: 604800000,
        minAllocation: 0.05,
        maxAllocation: 0.6,
        rebalanceThreshold: 0.1
      };
      this.fixesApplied.push({
        strategy: 'portfolio',
        fixes: ['Created missing portfolio configuration']
      });
    }

    const portfolio = config.portfolio;
    
    // Validate portfolio-level settings
    if (!portfolio.strategies || !Array.isArray(portfolio.strategies)) {
      portfolio.strategies = [];
      this.fixesApplied.push({
        strategy: 'portfolio',
        fixes: ['Fixed missing or invalid strategies array']
      });
    }

    // Validate and fix each strategy
    const validStrategies = [];
    let totalAllocation = 0;
    
    portfolio.strategies.forEach(strategyConfig => {
      const validStrategy = this.validateStrategyConfig(strategyConfig);
      if (validStrategy) {
        validStrategies.push(validStrategy);
        totalAllocation += validStrategy.allocation;
      }
    });

    // Normalize allocations to sum to 1.0
    if (validStrategies.length > 0 && Math.abs(totalAllocation - 1.0) > 0.001) {
      validStrategies.forEach(strategy => {
        strategy.allocation = strategy.allocation / totalAllocation;
      });
      this.fixesApplied.push({
        strategy: 'portfolio',
        fixes: [`Normalized allocations to sum to 1.0 (was ${totalAllocation.toFixed(3)})`]
      });
    }

    portfolio.strategies = validStrategies;
    
    // Validate portfolio settings
    const portfolioDefaults = {
      rebalanceFrequency: 86400000,
      performanceWindow: 604800000,
      minAllocation: 0.05,
      maxAllocation: 0.6,
      rebalanceThreshold: 0.1
    };
    
    Object.entries(portfolioDefaults).forEach(([key, defaultValue]) => {
      if (portfolio[key] === undefined) {
        portfolio[key] = defaultValue;
        this.fixesApplied.push({
          strategy: 'portfolio',
          fixes: [`Added missing portfolio setting: ${key} = ${defaultValue}`]
        });
      }
    });

    return config;
  }

  // Main validation function
  validateConfig(config) {
    this.validationErrors = [];
    this.fixesApplied = [];
    
    // Ensure basic structure
    if (!config || typeof config !== 'object') {
      config = {};
    }

    // Validate max trading balance
    if (config.maxTradingBalance !== undefined && 
        (typeof config.maxTradingBalance !== 'number' || config.maxTradingBalance <= 0)) {
      config.maxTradingBalance = null;
      this.fixesApplied.push({
        strategy: 'global',
        fixes: ['Reset invalid maxTradingBalance']
      });
    }

    // Validate portfolio configuration
    config = this.validatePortfolioConfig(config);
    
    // Add timestamp
    config.lastUpdated = new Date().toISOString();
    
    return {
      config,
      isValid: this.validationErrors.length === 0,
      errors: this.validationErrors,
      fixes: this.fixesApplied
    };
  }

  // Generate validation report
  generateReport(validationResult) {
    const { config, isValid, errors, fixes } = validationResult;
    
    let report = '\n=== Configuration Validation Report ===\n';
    
    if (isValid) {
      report += '✅ Configuration is valid\n';
    } else {
      report += '❌ Configuration has errors:\n';
      errors.forEach(error => {
        report += `  - ${error}\n`;
      });
    }
    
    if (fixes.length > 0) {
      report += '\n🔧 Fixes applied:\n';
      fixes.forEach(fix => {
        report += `  ${fix.strategy}:\n`;
        fix.fixes.forEach(f => {
          report += `    - ${f}\n`;
        });
      });
    }
    
    report += `\nStrategies configured: ${config.portfolio?.strategies?.length || 0}\n`;
    report += `Max trading balance: ${config.maxTradingBalance ? '$' + config.maxTradingBalance : 'Unlimited'}\n`;
    report += '=====================================\n';
    
    return report;
  }
}

// Convenience function to validate and fix config file
export async function validateConfigFile(configPath = 'bot-config.json') {
  const validator = new ConfigValidator();
  
  try {
    // Read existing config
    let config = {};
    if (fs.existsSync(configPath)) {
      const configData = fs.readFileSync(configPath, 'utf8');
      config = JSON.parse(configData);
    }
    
    // Validate and fix
    const result = validator.validateConfig(config);
    
    // Write back fixed config
    fs.writeFileSync(configPath, JSON.stringify(result.config, null, 2));
    
    // Generate and log report
    const report = validator.generateReport(result);
    console.log(report);
    
    return result;
  } catch (error) {
    console.error('Error validating config:', error.message);
    return {
      config: {},
      isValid: false,
      errors: [error.message],
      fixes: []
    };
  }
}
