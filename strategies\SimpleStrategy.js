import { BaseStrategy } from './BaseStrategy.js';

export class SimpleStrategy extends BaseStrategy {
  constructor(config = {}) {
    super({
      accountPercentToUse: 0.5,
      stopLossPercent: 0.025,
      ...config
    });
  }

  shouldEnterTrade(currentPrice, marketData) {
    // Simple strategy: always buy if we have no position
    return { 
      shouldEnter: true, 
      reason: 'Simple strategy: buy when no position held' 
    };
  }

  calculateStopLoss(currentPrice, marketData) {
    // Fixed percentage stop loss
    return {
      stopPercent: this.config.stopLossPercent,
      stopPrice: currentPrice * (1 - this.config.stopLossPercent),
      reason: `Fixed ${(this.config.stopLossPercent * 100)}% stop loss`
    };
  }

  getPositionSize(availableCash, currentPrice) {
    const toSpend = availableCash * this.config.accountPercentToUse;
    return {
      quantity: toSpend / currentPrice,
      dollarAmount: toSpend,
      reason: `Using ${(this.config.accountPercentToUse * 100)}% of available cash`
    };
  }

  getDescription() {
    return 'Basic strategy that buys immediately when no position is held and uses fixed percentage stop losses. Good for steady bull markets.';
  }

  getConfigSchema() {
    return {
      accountPercentToUse: {
        type: 'number',
        label: 'Account % to Use',
        min: 0.1,
        max: 1,
        step: 0.1,
        default: 0.5,
        description: 'Percentage of available cash to use for each trade'
      },
      stopLossPercent: {
        type: 'number',
        label: 'Stop Loss %',
        min: 0.01,
        max: 0.1,
        step: 0.005,
        default: 0.025,
        description: 'Fixed percentage for stop loss orders'
      }
    };
  }
}
