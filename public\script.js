// Global variables
const statusEl = document.getElementById('status');
const tradesEl = document.getElementById('trades');
const balanceEl = document.getElementById('balance');
const btcHoldingsEl = document.getElementById('btcHoldings');
const btcPriceEl = document.getElementById('btcPrice');
const portfolioValueEl = document.getElementById('portfolioValue');
const pnlEl = document.getElementById('pnl');
const stopLossEl = document.getElementById('stopLoss');
const logEl = document.getElementById('log');
const toggleBtn = document.getElementById('toggleBtn');
const strategySelect = document.getElementById('strategySelect');
const applyStrategyBtn = document.getElementById('applyStrategy');
const maxBalanceSlider = document.getElementById('maxBalanceSlider');
const maxBalanceValue = document.getElementById('maxBalanceValue');
const availableBalanceEl = document.getElementById('availableBalance');
const activeStrategiesEl = document.getElementById('activeStrategies');
const lastRebalanceEl = document.getElementById('lastRebalance');
const rebalanceFrequencyEl = document.getElementById('rebalanceFrequency');

let availableStrategies = {};
let currentStrategyConfig = {};
let currentAvailableBalance = 0;
let maxTradingBalance = 500;
let botRunning = false;
let priceChart;
let priceHistory = [];
let tradeEvents = [];
let currentTimeframe = '1d';

// Load available strategies from server
async function loadStrategies() {
  try {
    console.log('Loading strategies...');
    const response = await fetch('/api/strategies');
    console.log('Response status:', response.status);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      const text = await response.text();
      console.error('Expected JSON but got:', text.substring(0, 200));
      throw new Error('Server returned HTML instead of JSON - check server logs');
    }
    
    const data = await response.json();
    console.log('Strategies data:', data);
    
    availableStrategies = {};
    
    if (strategySelect) {
      strategySelect.innerHTML = '<option value="">Choose a strategy...</option>';
      
      if (data.strategies && Array.isArray(data.strategies)) {
        data.strategies.forEach(strategy => {
          console.log('Adding strategy:', strategy);
          availableStrategies[strategy.id] = strategy;
          
          const option = document.createElement('option');
          option.value = strategy.id;
          option.textContent = strategy.displayName;
          strategySelect.appendChild(option);
        });
      }
      
      const configGridEl = document.getElementById('configGrid');
      if (configGridEl) {
        updateStrategyUI();
      } else {
        console.log('Strategy UI elements not found, skipping UI update');
      }
    } else {
      console.warn('Strategy select element not found');
    }
  } catch (error) {
    console.error('Failed to load strategies:', error);
    if (strategySelect) {
      strategySelect.innerHTML = '<option value="">Error loading strategies</option>';
    }
  }
}

function updateStrategyUI() {
  const configGridEl = document.getElementById('configGrid');
  
  if (!strategySelect || !configGridEl) {
    console.warn('Required strategy UI elements not found');
    return;
  }
  
  const selectedStrategyId = strategySelect.value;
  const strategy = availableStrategies[selectedStrategyId];
  
  if (!strategy) return;
  
  configGridEl.innerHTML = '';
  currentStrategyConfig = {};
  
  if (strategy.config && Object.keys(strategy.config).length > 0) {
    Object.entries(strategy.config).forEach(([key, configItem]) => {
      const configRow = document.createElement('div');
      configRow.className = 'config-item';
      
      const label = document.createElement('label');
      label.textContent = configItem.label || key;
      label.setAttribute('for', key);
      
      const input = document.createElement('input');
      input.type = configItem.type || 'number';
      input.id = key;
      input.value = configItem.default || '';
      
      if (configItem.min !== undefined) input.min = configItem.min;
      if (configItem.max !== undefined) input.max = configItem.max;
      if (configItem.step !== undefined) input.step = configItem.step;
      if (configItem.description) input.title = configItem.description;
      
      input.addEventListener('input', (e) => {
        currentStrategyConfig[key] = configItem.type === 'number' ? 
          parseFloat(e.target.value) : e.target.value;
      });
      
      currentStrategyConfig[key] = configItem.default || '';
      
      configRow.appendChild(label);
      configRow.appendChild(input);
      configGridEl.appendChild(configRow);
    });
  }
}

// Load saved settings on page load
async function loadSavedSettings() {
  try {
    // Load max balance
    const balanceResponse = await fetch('/api/max-balance');
    if (balanceResponse.ok) {
      const contentType = balanceResponse.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        const balanceData = await balanceResponse.json();
        if (balanceData.maxBalance !== null && balanceData.maxBalance !== undefined) {
          maxTradingBalance = balanceData.maxBalance;
          
          if (maxBalanceSlider && maxBalanceValue) {
            maxBalanceSlider.value = maxTradingBalance;
            maxBalanceValue.textContent = maxTradingBalance.toFixed(0);
          }
        }
      }
    }
    
    // Load current strategy
    const strategyResponse = await fetch('/api/strategy');
    if (strategyResponse.ok) {
      const contentType = strategyResponse.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        const strategyData = await strategyResponse.json();
        if (strategyData.name) {
          const strategyKey = strategyData.name.toLowerCase().replace(/strategy$/, '').replace(/([A-Z])/g, '-$1').toLowerCase().replace(/^-/, '');
          if (availableStrategies[strategyKey]) {
            if (strategySelect) {
              strategySelect.value = strategyKey;
              updateStrategyUI();
            }
          }
        }
      }
    }
  } catch (error) {
    console.error('Failed to load saved settings:', error);
  }
}

// Update max trading balance
async function updateMaxTradingBalance(balance) {
  try {
    const response = await fetch('/api/max-balance', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ maxBalance: balance })
    });
    
    if (!response.ok) {
      console.error('Failed to update max trading balance');
    }
  } catch (error) {
    console.error('Error updating max trading balance:', error);
  }
}

// Update slider when balance changes
function updateBalanceSlider(availableBalance) {
  currentAvailableBalance = parseFloat(availableBalance) || 0;
  if (availableBalanceEl) {
    availableBalanceEl.textContent = currentAvailableBalance.toFixed(2);
  }
  
  if (maxBalanceSlider) {
    maxBalanceSlider.max = Math.max(currentAvailableBalance, 100);
    
    if (maxTradingBalance > currentAvailableBalance) {
      maxTradingBalance = currentAvailableBalance;
      maxBalanceSlider.value = maxTradingBalance;
      if (maxBalanceValue) {
        maxBalanceValue.textContent = maxTradingBalance.toFixed(0);
      }
      updateMaxTradingBalance(maxTradingBalance);
    }
  }
}

// Initialize the price chart
function initializeChart() {
  const ctx = document.getElementById('priceChart').getContext('2d');
  
  Chart.defaults.font.family = "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif";
  Chart.defaults.font.size = 12;
  Chart.defaults.color = '#9ca3af';
  
  priceChart = new Chart(ctx, {
    type: 'line',
    data: {
      datasets: [{
        label: 'BTC-USD',
        data: [],
        borderColor: '#00C805',
        backgroundColor: 'rgba(0, 200, 5, 0.1)',
        borderWidth: 2,
        pointRadius: 0,
        pointHoverRadius: 4,
        pointHoverBackgroundColor: '#00C805',
        pointHoverBorderColor: '#ffffff',
        pointHoverBorderWidth: 2,
        fill: true,
        tension: 0.1
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      interaction: {
        mode: 'index',
        intersect: false
      },
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          enabled: true,
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: '#ffffff',
          bodyColor: '#00C805',
          borderColor: '#2a2a2a',
          borderWidth: 1,
          cornerRadius: 4,
          padding: 10,
          displayColors: false,
          callbacks: {
            label: function(context) {
              return `$${context.parsed.y.toLocaleString()}`;
            }
          }
        },
        annotation: {
          annotations: {}
        }
      },
      scales: {
        x: {
          type: 'time',
          time: {
            unit: 'hour',
            displayFormats: {
              hour: 'HH:mm',
              day: 'MMM d'
            }
          },
          grid: {
            display: false,
            drawBorder: false
          },
          ticks: {
            maxRotation: 0,
            autoSkip: true,
            padding: 10
          }
        },
        y: {
          position: 'right',
          grid: {
            color: '#2a2a2a',
            drawBorder: false
          },
          ticks: {
            padding: 10,
            callback: function(value) {
              return '$' + value.toLocaleString();
            }
          },
          beginAtZero: false
        }
      }
    }
  });
  
  // Set up timeframe buttons
  document.querySelectorAll('.timeframe-btn').forEach(button => {
    button.addEventListener('click', () => {
      document.querySelectorAll('.timeframe-btn').forEach(b => b.classList.remove('active'));
      button.classList.add('active');
      currentTimeframe = button.dataset.period;
      updateChartTimeframe(currentTimeframe);
    });
  });
  
  console.log('Chart initialized successfully');
}

// Update chart with new price data
function updateChart(price, timestamp = new Date()) {
  if (!priceChart) {
    console.warn('Chart not initialized, skipping update');
    return;
  }
  
  const parsedPrice = parseFloat(price);
  if (isNaN(parsedPrice) || parsedPrice <= 0) {
    console.warn(`Invalid price value: ${price}`);
    return;
  }
  
  priceHistory.push({
    x: timestamp,
    y: parsedPrice
  });
  
  if (priceHistory.length > 1000) {
    priceHistory = priceHistory.slice(-1000);
  }
  
  priceChart.data.datasets[0].data = filterDataByTimeframe(priceHistory, currentTimeframe);
  updateTradeEventMarkers();
  priceChart.update('none');
}

// Add a trade event marker to the chart
function addTradeEvent(type, price, timestamp = new Date()) {
  tradeEvents.push({
    type: type,
    price: parseFloat(price),
    timestamp: timestamp
  });
  
  console.log(`Added ${type} event at $${price}`);
  
  updateTradeEventMarkers();
  if (priceChart) {
    priceChart.update('none');
  }
}

// Update trade event markers based on current timeframe
function updateTradeEventMarkers() {
  if (!priceChart) return;
  
  priceChart.options.plugins.annotation.annotations = {};
  
  const filteredEvents = filterEventsByTimeframe(tradeEvents, currentTimeframe);
  
  filteredEvents.forEach((event, index) => {
    priceChart.options.plugins.annotation.annotations[`event${index}`] = {
      type: 'point',
      xValue: event.timestamp,
      yValue: event.price,
      backgroundColor: event.type === 'buy' ? '#00C805' : '#FF6B35',
      borderColor: '#ffffff',
      borderWidth: 2,
      radius: 6,
      label: {
        enabled: true,
        content: event.type === 'buy' ? '↑ BUY' : '↓ SELL',
        position: event.type === 'buy' ? 'bottom' : 'top',
        backgroundColor: event.type === 'buy' ? '#00C805' : '#FF6B35',
        color: '#000000',
        font: {
          weight: 'bold',
          size: 11
        },
        padding: 4,
        yAdjust: event.type === 'buy' ? -8 : 8
      }
    };
  });
}

// Filter data based on timeframe
function filterDataByTimeframe(data, timeframe) {
  if (!data.length) return [];
  
  const now = new Date();
  let cutoff;
  
  switch (timeframe) {
    case '1d':
      cutoff = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      if (priceChart) priceChart.options.scales.x.time.unit = 'hour';
      break;
    case '1w':
      cutoff = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      if (priceChart) priceChart.options.scales.x.time.unit = 'day';
      break;
    case '1m':
      cutoff = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      if (priceChart) priceChart.options.scales.x.time.unit = 'day';
      break;
    case '3m':
      cutoff = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      if (priceChart) priceChart.options.scales.x.time.unit = 'month';
      break;
    default:
      cutoff = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      if (priceChart) priceChart.options.scales.x.time.unit = 'hour';
  }
  
  return data.filter(point => new Date(point.x) >= cutoff);
}

// Filter events based on timeframe
function filterEventsByTimeframe(events, timeframe) {
  if (!events.length) return [];
  
  const now = new Date();
  let cutoff;
  
  switch (timeframe) {
    case '1d':
      cutoff = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      break;
    case '1w':
      cutoff = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case '1m':
      cutoff = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
    case '3m':
      cutoff = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      break;
    default:
      cutoff = new Date(now.getTime() - 24 * 60 * 60 * 1000);
  }
  
  return events.filter(event => new Date(event.timestamp) >= cutoff);
}

// Update chart timeframe
function updateChartTimeframe(timeframe) {
  if (!priceChart) return;
  
  priceChart.data.datasets[0].data = filterDataByTimeframe(priceHistory, timeframe);
  updateTradeEventMarkers();
  priceChart.update();
}

// WebSocket connection
const ws = new WebSocket('ws://' + location.hostname + ':8182');

ws.onmessage = (event) => {
  try {
    const data = JSON.parse(event.data);
    botRunning = data.botRunning;
    
    // Handle historical prices on initial connection
    if (data.historicalPrices && data.historicalPrices.length > 0) {
      console.log(`Received ${data.historicalPrices.length} historical price points`);
      
      priceHistory = [];
      
      data.historicalPrices.forEach(point => {
        priceHistory.push({
          x: new Date(point.timestamp),
          y: point.price
        });
      });
      
      if (priceChart) {
        priceChart.data.datasets[0].data = filterDataByTimeframe(priceHistory, currentTimeframe);
        priceChart.update();
      }
      
      console.log(`Loaded ${priceHistory.length} price points into chart`);
    }
    
    // Handle new single price point updates
    if (data.newPricePoint) {
      const newPoint = {
        x: new Date(data.newPricePoint.timestamp),
        y: data.newPricePoint.price
      };
      
      priceHistory.push(newPoint);
      
      if (priceHistory.length > 1000) {
        priceHistory = priceHistory.slice(-1000);
      }
      
      if (priceChart) {
        priceChart.data.datasets[0].data = filterDataByTimeframe(priceHistory, currentTimeframe);
        priceChart.update('none');
      }
    }
    
    // Fallback: Update chart with current price if available
    else if (data.btcPrice && !data.historicalPrices) {
      const price = parseFloat(data.btcPrice);
      if (price > 0) {
        console.log(`Received BTC price: $${price}`);
        updateChart(price);
      }
    }
    
    // Check for trade events in logs
    if (data.logs && data.logs.length) {
      const lastLog = data.logs[data.logs.length - 1];
      
      if (lastLog.includes('BUY order placed:') && data.btcPrice) {
        addTradeEvent('buy', data.btcPrice);
      }
      
      if (lastLog.includes('SELL order placed:') && data.btcPrice) {
        addTradeEvent('sell', data.btcPrice);
      }
    }
    
    // Update status
    statusEl.textContent = botRunning ? 'Running' : 'Stopped';
    statusEl.className = `stat-value ${botRunning ? 'status-running' : 'status-stopped'}`;
    
    // Update basic stats
    tradesEl.textContent = data.trades || 0;
    
    // Update financial data
    if (data.balance) {
      balanceEl.textContent = `$${parseFloat(data.balance).toFixed(2)}`;
    }

    if (data.btcHoldings) {
      btcHoldingsEl.textContent = parseFloat(data.btcHoldings).toFixed(8);
    }

    if (data.btcPrice) {
      btcPriceEl.textContent = `$${parseFloat(data.btcPrice).toFixed(2)}`;
    }

    if (data.portfolioValue) {
      portfolioValueEl.textContent = `$${parseFloat(data.portfolioValue).toFixed(2)}`;
    }

    if (data.pnl !== undefined) {
      const pnlValue = parseFloat(data.pnl);
      pnlEl.textContent = `${pnlValue >= 0 ? '+' : ''}$${pnlValue.toFixed(2)}`;
      pnlEl.className = `stat-value ${pnlValue >= 0 ? 'positive' : 'negative'}`;
    }

    if (data.stopLoss) {
      stopLossEl.textContent = `$${parseFloat(data.stopLoss).toFixed(2)}`;
    } else {
      stopLossEl.textContent = 'None';
    }

    // Update available balance for slider
    if (data.balance) {
      updateBalanceSlider(data.balance);
    }

    // Update strategy status if available
    if (data.strategyStatus && data.strategyStatus.strategies) {
      updateStrategyStatusDisplay(data.strategyStatus);
    }

    // Update logs
    if (data.logs && data.logs.length) {
      logEl.innerHTML = data.logs.slice(-100).map(l => `<div class="log-entry">${l}</div>`).join('');
      logEl.scrollTop = logEl.scrollHeight;
    }

    toggleBtn.textContent = botRunning ? 'Stop Bot' : 'Start Bot';
  } catch (error) {
    console.error('Error processing WebSocket message:', error);
  }
};

ws.onopen = () => {
  console.log('WebSocket connected');
};

ws.onclose = () => {
  console.log('WebSocket disconnected');
};

ws.onerror = (error) => {
  console.error('WebSocket error:', error);
};

// Event listeners - add null checks
document.addEventListener('DOMContentLoaded', function() {
  console.log('DOM loaded, initializing...');
  initializeChart();
  initializePage();
});

// Initialize page on load
async function initializePage() {
  console.log('Initializing page...');
  await loadPortfolioStrategies();
  await loadSavedSettings();
  console.log('Page initialization complete');
}

// Toggle bot
if (toggleBtn) {
  toggleBtn.addEventListener('click', () => {
    fetch('/api/remote', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: botRunning ? 'stop' : 'start' })
    });
  });
}

if (maxBalanceSlider && maxBalanceValue) {
  maxBalanceSlider.addEventListener('input', (e) => {
    maxTradingBalance = parseFloat(e.target.value);
    maxBalanceValue.textContent = maxTradingBalance.toFixed(0);
    updateMaxTradingBalance(maxTradingBalance);
  });
  
  // Initialize slider value
  maxBalanceValue.textContent = maxBalanceSlider.value;
}

// Fetch and update portfolio data
async function updatePortfolioData() {
  try {
    const response = await fetch('/api/portfolio');
    if (response.ok) {
      const data = await response.json();
      updatePortfolioDisplay(data);
    } else {
      console.error('Portfolio API response not ok:', response.status, response.statusText);
    }
  } catch (error) {
    console.error('Error fetching portfolio data:', error);
  }
}

// Update portfolio display - updates existing strategy cards instead of replacing them
function updatePortfolioDisplay(data) {

  // Update overview stats
  const activeStrategiesEl = document.getElementById('activeStrategies');
  const lastRebalanceEl = document.getElementById('lastRebalance');
  const rebalanceFrequencyEl = document.getElementById('rebalanceFrequency');

  if (activeStrategiesEl) activeStrategiesEl.textContent = Object.keys(data.strategies).length;
  if (lastRebalanceEl) lastRebalanceEl.textContent = new Date(data.lastRebalance).toLocaleString();
  if (rebalanceFrequencyEl) rebalanceFrequencyEl.textContent = data.rebalanceFrequency;

  // Update existing strategy cards instead of replacing them
  const strategiesGrid = document.getElementById('strategiesGrid');
  if (!strategiesGrid) {
    console.error('strategiesGrid element not found!');
    return;
  }

  // Update allocation badges and stats in existing strategy control cards
  const effectiveBalance = parseFloat(data.totalBalance || 0);
  const actualBalance = parseFloat(data.actualBalance || 0);
  const isLimited = data.maxTradingBalance !== null && actualBalance > data.maxTradingBalance;

  Object.entries(data.strategies).forEach(([strategyName, stats]) => {
    // Find the strategy card by its data attribute (much more reliable)
    const strategyCard = document.querySelector(`.strategy-control-card[data-strategy-id="${strategyName}"]`);

    if (strategyCard) {
      // Update allocation badge
      const allocationBadge = strategyCard.querySelector('.allocation-badge');
      if (allocationBadge) {
        allocationBadge.textContent = stats.allocation;
      }

      // Calculate and update bucket size
      const rawAllocation = data.allocations ? data.allocations[strategyName] : 0;
      const bucketSize = effectiveBalance * rawAllocation;

      // Update stats section (bucket size and performance stats)
      const statsSection = strategyCard.querySelector('.strategy-stats');
      if (statsSection) {
        // Find stats by their labels instead of position (more reliable)
        const statRows = statsSection.querySelectorAll('.stat-row');

        statRows.forEach(row => {
          const label = row.querySelector('span:first-child')?.textContent;
          const valueEl = row.querySelector('span:last-child');

          if (label && valueEl) {
            switch (label) {
              case 'Bucket Size:':
                valueEl.textContent = `$${bucketSize.toFixed(2)}${isLimited ? ' (limited)' : ''}`;
                break;
              case 'Total Return:':
                valueEl.textContent = stats.totalReturn;
                valueEl.className = parseFloat(stats.totalReturn) >= 0 ? 'positive' : 'negative';
                break;
              case 'Win Rate:':
                valueEl.textContent = stats.winRate;
                break;
              case 'Trades:':
                valueEl.textContent = stats.trades;
                break;
              case 'Sharpe Ratio:':
                valueEl.textContent = stats.sharpeRatio;
                break;
            }
          }
        });
      }
    } else {
      console.warn(`Could not find card for strategy: ${strategyName}`);
    }
  });
}

// Call updatePortfolioData on page load
document.addEventListener('DOMContentLoaded', () => {
  updatePortfolioData();
});

// Set interval to update portfolio data every 30 seconds
setInterval(updatePortfolioData, 30000);

// Update status function - add portfolio update
async function updateStatus() {
  try {
    const response = await fetch('/api/status');
    const data = await response.json();
    
    // ... existing status updates ...
    
    // Update portfolio data
    await updatePortfolioData();
    
  } catch (error) {
    console.error('Error updating status:', error);
  }
}

// Load and display all available strategies with portfolio data
async function loadPortfolioStrategies() {
  try {
    // Load available strategies
    const strategiesResponse = await fetch('/api/strategies');
    const strategiesData = await strategiesResponse.json();

    // Load current portfolio data with status information
    const portfolioResponse = await fetch('/api/portfolio/status');
    const portfolioData = portfolioResponse.ok ? await portfolioResponse.json() : { strategies: {}, allocations: {} };

    displayStrategiesGrid(strategiesData.strategies, portfolioData);

  } catch (error) {
    console.error('Error loading portfolio strategies:', error);
  }
}

// Display strategies grid with controls
function displayStrategiesGrid(availableStrategies, portfolioData) {
  const strategiesGrid = document.getElementById('strategiesGrid');
  strategiesGrid.innerHTML = '';

  let activeCount = 0;
  const effectiveBalance = parseFloat(portfolioData.totalBalance || 0);
  const actualBalance = parseFloat(portfolioData.actualBalance || 0);
  const isLimited = portfolioData.maxTradingBalance !== null && actualBalance > portfolioData.maxTradingBalance;
  
  availableStrategies.forEach(strategy => {
    const isActive = portfolioData.strategies && portfolioData.strategies[strategy.id];
    const stats = isActive ? portfolioData.strategies[strategy.id] : null;
    const allocation = portfolioData.allocations ? portfolioData.allocations[strategy.id] : 0;
    
    if (isActive) activeCount++;
    
    const bucketSize = effectiveBalance * allocation;
    
    const strategyCard = document.createElement('div');
    strategyCard.className = `strategy-control-card ${isActive ? 'active' : 'inactive'}`;
    strategyCard.setAttribute('data-strategy-id', strategy.id);
    strategyCard.innerHTML = `
      <div class="strategy-header">
        <div class="strategy-title">
          <h4>${strategy.displayName}</h4>
          <div class="strategy-toggle">
            <label class="toggle-switch">
              <input type="checkbox" ${isActive ? 'checked' : ''} 
                     onchange="toggleStrategy('${strategy.id}', this.checked)">
              <span class="toggle-slider"></span>
            </label>
          </div>
        </div>
        ${isActive ? `<div class="allocation-badge">${(allocation * 100).toFixed(1)}%</div>` : ''}
      </div>
      
      <div class="strategy-description">
        <p>${strategy.description}</p>
      </div>
      
      ${isActive ? `
        <div class="strategy-status">
          <div class="status-section">
            <h5>Current Status</h5>
            <div class="status-item">
              <span class="status-label">Last Decision:</span>
              <span class="status-value ${stats.status?.lastDecision ? 'positive' : 'neutral'}">${stats.status?.lastDecision ? 'ENTER' : 'WAIT'}</span>
            </div>
            <div class="status-item">
              <span class="status-label">Reason:</span>
              <span class="status-reason">${stats.status?.lastDecisionReason || 'No recent decisions'}</span>
            </div>
            <div class="status-item">
              <span class="status-label">Next Signal:</span>
              <span class="status-next">${stats.status?.nextSignal || 'Monitoring market'}</span>
            </div>
            ${stats.status?.lastTradeTime ? `
              <div class="status-item">
                <span class="status-label">Last Trade:</span>
                <span class="status-trade">${stats.status.lastTradeType?.toUpperCase()} at $${stats.status.lastTradePrice?.toFixed(2)} (${new Date(stats.status.lastTradeTime).toLocaleString()})</span>
              </div>
            ` : ''}
          </div>
        </div>

        <div class="strategy-stats">
          <div class="stat-row">
            <span>Bucket Size:</span>
            <span>$${bucketSize.toFixed(2)}${isLimited ? ' (limited)' : ''}</span>
          </div>
          <div class="stat-row">
            <span>Total Return:</span>
            <span class="${parseFloat(stats.totalReturn) >= 0 ? 'positive' : 'negative'}">${stats.totalReturn}</span>
          </div>
          <div class="stat-row">
            <span>Win Rate:</span>
            <span>${stats.winRate}</span>
          </div>
          <div class="stat-row">
            <span>Trades:</span>
            <span>${stats.trades}</span>
          </div>
          <div class="stat-row">
            <span>Sharpe Ratio:</span>
            <span>${stats.sharpeRatio}</span>
          </div>
        </div>
      ` : `
        <div class="strategy-inactive">
          <p>Strategy is currently disabled</p>
        </div>
      `}
    `;
    strategiesGrid.appendChild(strategyCard);
  });
  
  // Update active strategies count
  document.getElementById('activeStrategies').textContent = activeCount;
  
  // Update other portfolio stats if available
  if (portfolioData.lastRebalance) {
    document.getElementById('lastRebalance').textContent = new Date(portfolioData.lastRebalance).toLocaleString();
  }
  if (portfolioData.rebalanceFrequency) {
    document.getElementById('rebalanceFrequency').textContent = portfolioData.rebalanceFrequency;
  }
}

// Update strategy status display in real-time
function updateStrategyStatusDisplay(strategyData) {
  if (!strategyData.strategies) return;

  Object.entries(strategyData.strategies).forEach(([strategyName, stats]) => {
    // Find the strategy card by looking for the strategy name in the title
    const strategyCards = document.querySelectorAll('.strategy-control-card');

    strategyCards.forEach(card => {
      const titleElement = card.querySelector('.strategy-title h4');
      if (titleElement && titleElement.textContent.toLowerCase().includes(strategyName.replace('-', ' '))) {
        // Update status section if it exists
        const statusSection = card.querySelector('.strategy-status');
        if (statusSection && stats.status) {
          const lastDecisionValue = statusSection.querySelector('.status-value');
          const reasonElement = statusSection.querySelector('.status-reason');
          const nextSignalElement = statusSection.querySelector('.status-next');

          if (lastDecisionValue) {
            lastDecisionValue.textContent = stats.status.lastDecision ? 'ENTER' : 'WAIT';
            lastDecisionValue.className = `status-value ${stats.status.lastDecision ? 'positive' : 'neutral'}`;
          }

          if (reasonElement) {
            reasonElement.textContent = stats.status.lastDecisionReason || 'No recent decisions';
          }

          if (nextSignalElement) {
            nextSignalElement.textContent = stats.status.nextSignal || 'Monitoring market';
          }
        }
      }
    });
  });
}

// Toggle strategy on/off
async function toggleStrategy(strategyId, enabled) {
  try {
    const response = await fetch('/api/portfolio/strategy/toggle', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ strategyName: strategyId, enabled })
    });

    if (response.ok) {
      // Reload the strategies display
      await loadPortfolioStrategies();
      console.log(`${enabled ? 'Enabled' : 'Disabled'} strategy: ${strategyId}`);
    } else {
      throw new Error('Failed to toggle strategy');
    }
  } catch (error) {
    console.error('Error toggling strategy:', error);
    // Reload to reset the toggle state
    await loadPortfolioStrategies();
  }
}







