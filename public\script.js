// Global variables
console.log('🚀 Script.js loading...');
console.log('📋 Defining global variables...');

// DOM elements - will be initialized after DOM loads
let statusEl, tradesEl, balanceEl, btcHoldingsEl, btcPriceEl, portfolioValueEl, pnlEl, stopLossEl, logEl;
let toggleBtn, strategySelect, applyStrategyBtn, maxBalanceSlider, maxBalanceValue;
let availableBalanceEl, activeStrategiesEl, lastRebalanceEl, rebalanceFrequencyEl;

console.log('✅ Global variables defined successfully');

let availableStrategies = {};
let currentStrategyConfig = {};
let currentAvailableBalance = 0;
let maxTradingBalance = 500;
let botRunning = false;
let priceChart;
let portfolioChart;
let priceHistory = [];
let portfolioHistory = [];
let tradeEvents = [];
let currentTimeframe = '5m';
let currentPeriod = '1d';

// Load available strategies from server
async function loadStrategies() {
  try {
    console.log('Loading strategies...');
    const response = await fetch('/api/strategies');
    console.log('Response status:', response.status);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      const text = await response.text();
      console.error('Expected JSON but got:', text.substring(0, 200));
      throw new Error('Server returned HTML instead of JSON - check server logs');
    }

    const data = await response.json();
    console.log('Strategies data:', data);

    availableStrategies = {};

    if (strategySelect) {
      strategySelect.innerHTML = '<option value="">Choose a strategy...</option>';

      if (data.strategies && Array.isArray(data.strategies)) {
        data.strategies.forEach(strategy => {
          console.log('Adding strategy:', strategy);
          availableStrategies[strategy.id] = strategy;

          const option = document.createElement('option');
          option.value = strategy.id;
          option.textContent = strategy.displayName;
          strategySelect.appendChild(option);
        });
      }

      const configGridEl = document.getElementById('configGrid');
      if (configGridEl) {
        updateStrategyUI();
      } else {
        console.log('Strategy UI elements not found, skipping UI update');
      }
    } else {
      console.warn('Strategy select element not found');
    }
  } catch (error) {
    console.error('Failed to load strategies:', error);
    if (strategySelect) {
      strategySelect.innerHTML = '<option value="">Error loading strategies</option>';
    }
  }
}

function updateStrategyUI() {
  const configGridEl = document.getElementById('configGrid');

  if (!strategySelect || !configGridEl) {
    console.warn('Required strategy UI elements not found');
    return;
  }

  const selectedStrategyId = strategySelect.value;
  const strategy = availableStrategies[selectedStrategyId];

  if (!strategy) return;

  configGridEl.innerHTML = '';
  currentStrategyConfig = {};

  if (strategy.config && Object.keys(strategy.config).length > 0) {
    Object.entries(strategy.config).forEach(([key, configItem]) => {
      const configRow = document.createElement('div');
      configRow.className = 'config-item';

      const label = document.createElement('label');
      label.textContent = configItem.label || key;
      label.setAttribute('for', key);

      const input = document.createElement('input');
      input.type = configItem.type || 'number';
      input.id = key;
      input.value = configItem.default || '';

      if (configItem.min !== undefined) input.min = configItem.min;
      if (configItem.max !== undefined) input.max = configItem.max;
      if (configItem.step !== undefined) input.step = configItem.step;
      if (configItem.description) input.title = configItem.description;

      input.addEventListener('input', (e) => {
        currentStrategyConfig[key] = configItem.type === 'number' ?
          parseFloat(e.target.value) : e.target.value;
      });

      currentStrategyConfig[key] = configItem.default || '';

      configRow.appendChild(label);
      configRow.appendChild(input);
      configGridEl.appendChild(configRow);
    });
  }
}

// Load saved settings on page load
async function loadSavedSettings() {
  try {
    // Load max balance
    const balanceResponse = await fetch('/api/max-balance');
    if (balanceResponse.ok) {
      const contentType = balanceResponse.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        const balanceData = await balanceResponse.json();
        if (balanceData.maxBalance !== null && balanceData.maxBalance !== undefined) {
          maxTradingBalance = balanceData.maxBalance;

          if (maxBalanceSlider && maxBalanceValue) {
            maxBalanceSlider.value = maxTradingBalance;
            maxBalanceValue.textContent = maxTradingBalance.toFixed(0);
          }
        }
      }
    }

    // Load current strategy
    const strategyResponse = await fetch('/api/strategy');
    if (strategyResponse.ok) {
      const contentType = strategyResponse.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        const strategyData = await strategyResponse.json();
        if (strategyData.name) {
          const strategyKey = strategyData.name.toLowerCase().replace(/strategy$/, '').replace(/([A-Z])/g, '-$1').toLowerCase().replace(/^-/, '');
          if (availableStrategies[strategyKey]) {
            if (strategySelect) {
              strategySelect.value = strategyKey;
              updateStrategyUI();
            }
          }
        }
      }
    }
  } catch (error) {
    console.error('Failed to load saved settings:', error);
  }
}

// Update max trading balance
async function updateMaxTradingBalance(balance) {
  try {
    const response = await fetch('/api/max-balance', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ maxBalance: balance })
    });

    if (!response.ok) {
      console.error('Failed to update max trading balance');
    }
  } catch (error) {
    console.error('Error updating max trading balance:', error);
  }
}

// Update slider when balance changes
function updateBalanceSlider(availableBalance) {
  currentAvailableBalance = parseFloat(availableBalance) || 0;
  if (availableBalanceEl) {
    availableBalanceEl.textContent = currentAvailableBalance.toFixed(2);
  }

  if (maxBalanceSlider) {
    maxBalanceSlider.max = Math.max(currentAvailableBalance, 100);

    if (maxTradingBalance > currentAvailableBalance) {
      maxTradingBalance = currentAvailableBalance;
      maxBalanceSlider.value = maxTradingBalance;
      if (maxBalanceValue) {
        maxBalanceValue.textContent = maxTradingBalance.toFixed(0);
      }
      updateMaxTradingBalance(maxTradingBalance);
    }
  }
}

// Initialize both charts
function initializeChart() {
  initializePortfolioChart();
  initializePriceChart();
  initializeChartControls();
  console.log('Charts initialized successfully');
}

// Initialize portfolio balance chart
function initializePortfolioChart() {
  const ctx = document.getElementById('portfolioChart').getContext('2d');

  portfolioChart = new Chart(ctx, {
    type: 'line',
    data: {
      datasets: [
        {
          label: 'Total Value',
          data: [],
          borderColor: '#10b981',
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          borderWidth: 2,
          fill: true,
          tension: 0.1,
          pointRadius: 0,
          pointHoverRadius: 4
        },
        {
          label: 'Cash Balance',
          data: [],
          borderColor: '#3b82f6',
          backgroundColor: 'rgba(59, 130, 246, 0.05)',
          borderWidth: 1,
          fill: false,
          tension: 0.1,
          pointRadius: 0,
          pointHoverRadius: 4
        },
        {
          label: 'Holdings Value',
          data: [],
          borderColor: '#f59e0b',
          backgroundColor: 'rgba(245, 158, 11, 0.05)',
          borderWidth: 1,
          fill: false,
          tension: 0.1,
          pointRadius: 0,
          pointHoverRadius: 4
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      interaction: {
        intersect: false,
        mode: 'index'
      },
      plugins: {
        legend: {
          display: true,
          position: 'top',
          labels: {
            color: '#9ca3af',
            usePointStyle: true,
            pointStyle: 'line'
          }
        },
        tooltip: {
          backgroundColor: 'rgba(17, 24, 39, 0.95)',
          titleColor: '#ffffff',
          bodyColor: '#9ca3af',
          borderColor: '#374151',
          borderWidth: 1,
          cornerRadius: 8,
          callbacks: {
            title: function (context) {
              return new Date(context[0].parsed.x).toLocaleString();
            },
            label: function (context) {
              return `${context.dataset.label}: $${context.parsed.y.toFixed(2)}`;
            }
          }
        }
      },
      scales: {
        x: {
          type: 'time',
          time: {
            unit: 'hour',
            displayFormats: {
              hour: 'HH:mm',
              day: 'MMM dd',
              month: 'MMM yyyy'
            }
          },
          grid: {
            color: '#374151',
            drawBorder: false
          },
          ticks: {
            color: '#9ca3af',
            maxTicksLimit: 6
          }
        },
        y: {
          grid: {
            color: '#374151',
            drawBorder: false
          },
          ticks: {
            color: '#9ca3af',
            callback: function (value) {
              return '$' + value.toFixed(0);
            }
          }
        }
      },
      animation: {
        duration: 0
      }
    }
  });
}

// Initialize OHLC candlestick chart
function initializePriceChart() {
  console.log('🔧 Initializing price chart...');
  const ctx = document.getElementById('priceChart').getContext('2d');

  Chart.defaults.font.family = "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif";
  Chart.defaults.font.size = 12;
  Chart.defaults.color = '#9ca3af';

  // Test if candlestick controller is available
  console.log('Available chart types:', Object.keys(Chart.registry.controllers.items));
  console.log('Candlestick controller available:', !!Chart.registry.getController('candlestick'));

  // Try candlestick chart first, fallback to line chart if plugin not available
  const chartType = Chart.registry.getController('candlestick') ? 'candlestick' : 'line';
  console.log(`Using chart type: ${chartType}`);

  if (chartType === 'candlestick') {
    priceChart = new Chart(ctx, {
      type: 'candlestick',
      data: {
        datasets: [
          {
            label: 'BTC-USD (Completed)',
            data: [],
            borderColor: '#10b981',
            backgroundColor: '#ef4444',
            borderWidth: 1,
            color: {
              up: '#10b981',
              down: '#ef4444',
              unchanged: '#9ca3af'
            }
          },
          {
            label: 'BTC-USD (Forming)',
            data: [],
            borderColor: '#3b82f6',
            backgroundColor: '#f59e0b',
            borderWidth: 2,
            color: {
              up: '#3b82f6',
              down: '#f59e0b',
              unchanged: '#6b7280'
            }
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
          intersect: false,
          mode: 'index'
        },
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            backgroundColor: 'rgba(17, 24, 39, 0.95)',
            titleColor: '#ffffff',
            bodyColor: '#9ca3af',
            borderColor: '#374151',
            borderWidth: 1,
            cornerRadius: 8,
            displayColors: false,
            callbacks: {
              title: function (context) {
                const isForming = context[0].datasetIndex === 1; // Second dataset is forming candles
                const timeStr = new Date(context[0].parsed.x).toLocaleString();
                return isForming ? `${timeStr} (Forming)` : timeStr;
              },
              label: function (context) {
                const data = context.parsed;
                const isForming = context.datasetIndex === 1;
                const labels = [
                  `Open: $${data.o.toFixed(2)}`,
                  `High: $${data.h.toFixed(2)}`,
                  `Low: $${data.l.toFixed(2)}`,
                  `Close: $${data.c.toFixed(2)}`
                ];

                if (isForming) {
                  labels.push('⏱️ Candle is still forming');
                }

                return labels;
              }
            }
          },
          annotation: {
            annotations: {}
          }
        },
        scales: {
          x: {
            type: 'linear',
            position: 'bottom',
            grid: {
              color: '#374151',
              drawBorder: false
            },
            ticks: {
              color: '#9ca3af',
              maxTicksLimit: 8,
              callback: function (value) {
                return new Date(value).toLocaleTimeString();
              }
            }
          },
          y: {
            type: 'linear',
            beginAtZero: false,
            grid: {
              color: '#374151',
              drawBorder: false
            },
            ticks: {
              color: '#9ca3af',
              callback: function (value) {
                return '$' + value.toLocaleString();
              }
            }
          }
        },
        animation: {
          duration: 0
        }
      }
    });
  }

  // Initialize chart controls
  function initializeChartControls() {
    // Timeframe buttons (for candle timeframe)
    document.querySelectorAll('.timeframe-btn').forEach(button => {
      button.addEventListener('click', () => {
        document.querySelectorAll('.timeframe-btn').forEach(b => b.classList.remove('active'));
        button.classList.add('active');
        currentTimeframe = button.dataset.timeframe;
        updateCharts();
      });
    });

    // Period buttons (for time range)
    document.querySelectorAll('.period-btn').forEach(button => {
      button.addEventListener('click', () => {
        document.querySelectorAll('.period-btn').forEach(b => b.classList.remove('active'));
        button.classList.add('active');
        currentPeriod = button.dataset.period;
        updateCharts();
      });
    });
  }

  // Update both charts with new data
  async function updateCharts() {
    await updatePriceChart();
    await updatePortfolioChart();
  }

  // Update OHLC candlestick chart
  async function updatePriceChart() {
    if (!priceChart) {
      console.warn('Price chart not initialized, skipping update');
      return;
    }

    try {
      let response = await fetch(`/api/candles/${currentTimeframe}?period=${currentPeriod}`);
      let data = await response.json();

      // If no candles for current timeframe, try 1m as fallback
      if (data.count === 0 && currentTimeframe !== '1m') {
        console.log(`No ${currentTimeframe} candles, trying 1m as fallback`);
        response = await fetch(`/api/candles/1m?period=${currentPeriod}`);
        data = await response.json();
      }

      console.log(`Chart update: ${data.count} candles for ${data.timeframe}`, data);

      if (data.candles && data.candles.length > 0) {
        // Validate candle data format
        const validCandles = data.candles.filter(candle =>
          candle.t && typeof candle.o === 'number' && typeof candle.h === 'number' &&
          typeof candle.l === 'number' && typeof candle.c === 'number'
        );

        if (validCandles.length === 0) {
          console.warn('No valid candle data found');
          return;
        }

        // Convert to Chart.js candlestick format
        const formatCandleForChart = (candle) => ({
          x: candle.t,
          o: candle.o,
          h: candle.h,
          l: candle.l,
          c: candle.c
        });

        // Separate completed and forming candles
        const completedCandles = validCandles.filter(candle => !candle.isForming).map(formatCandleForChart);
        const formingCandles = validCandles.filter(candle => candle.isForming).map(formatCandleForChart);

        console.log(`Completed: ${completedCandles.length}, Forming: ${formingCandles.length}`);

        // Log sample data for debugging
        if (completedCandles.length > 0 || formingCandles.length > 0) {
          const sample = completedCandles[0] || formingCandles[0];
          console.log(`Sample formatted candle:`, sample);
          console.log(`OHLC values: O:${sample.o}, H:${sample.h}, L:${sample.l}, C:${sample.c}`);
        }

        // Update completed candles dataset
        priceChart.data.datasets[0].data = completedCandles;
        console.log(`Set completed candles:`, completedCandles);

        // Update forming candles dataset
        priceChart.data.datasets[1].data = formingCandles;
        console.log(`Set forming candles:`, formingCandles);



        // Update chart with error handling
        try {
          priceChart.update('none');
          console.log('Chart updated successfully');
        } catch (chartError) {
          console.error('Chart update error:', chartError);
          // Try to reinitialize the chart if update fails
          initializePriceChart();
        }
      } else {
        console.log('No candle data available yet');
      }
    } catch (error) {
      console.error('Error updating price chart:', error);
    }
  }

  // Update portfolio balance chart
  async function updatePortfolioChart() {
    if (!portfolioChart) {
      console.warn('Portfolio chart not initialized, skipping update');
      return;
    }

    try {
      const response = await fetch(`/api/portfolio/balance-history?period=${currentPeriod}`);
      const data = await response.json();

      if (data.balanceHistory && data.balanceHistory.length > 0) {
        const totalValueData = data.balanceHistory.map(point => ({
          x: point.timestamp,
          y: point.totalValue
        }));

        const cashBalanceData = data.balanceHistory.map(point => ({
          x: point.timestamp,
          y: point.cashBalance
        }));

        const holdingsValueData = data.balanceHistory.map(point => ({
          x: point.timestamp,
          y: point.holdingsValue
        }));

        portfolioChart.data.datasets[0].data = totalValueData;
        portfolioChart.data.datasets[1].data = cashBalanceData;
        portfolioChart.data.datasets[2].data = holdingsValueData;
        portfolioChart.update('none');

        // Update balance summary
        updateBalanceSummary(data);
      }
    } catch (error) {
      console.error('Error updating portfolio chart:', error);
    }
  }

  // Update balance summary display
  function updateBalanceSummary(data) {
    const cashElement = document.getElementById('cashBalance');
    const holdingsElement = document.getElementById('holdingsValue');
    const totalElement = document.getElementById('totalValue');
    const pnlElement = document.getElementById('pnlDisplay');

    if (cashElement) cashElement.textContent = `$${data.currentBalance.toFixed(2)}`;
    if (holdingsElement) holdingsElement.textContent = `$${data.currentBtcValue.toFixed(2)}`;
    if (totalElement) totalElement.textContent = `$${data.totalValue.toFixed(2)}`;

    if (pnlElement) {
      const pnl = data.totalValue - 100; // Assuming $100 starting balance
      const pnlPercent = (pnl / 100) * 100;
      pnlElement.textContent = `P&L: $${pnl.toFixed(2)} (${pnlPercent.toFixed(1)}%)`;

      // Update color based on P&L
      pnlElement.classList.remove('positive', 'negative');
      if (pnl > 0) {
        pnlElement.classList.add('positive');
      } else if (pnl < 0) {
        pnlElement.classList.add('negative');
      }
    }
  }

  // Legacy function for backward compatibility
  function updateChart(price, timestamp = new Date()) {
    // This function is kept for backward compatibility
    // The new system uses updateCharts() which fetches OHLC data
    console.log('Legacy updateChart called, consider using updateCharts() instead');
  }

  // Add a trade event marker to the chart
  function addTradeEvent(type, price, timestamp = new Date()) {
    tradeEvents.push({
      type: type,
      price: parseFloat(price),
      timestamp: timestamp
    });

    console.log(`Added ${type} event at $${price}`);

    updateTradeEventMarkers();
    if (priceChart) {
      priceChart.update('none');
    }
  }

  // Update trade event markers based on current timeframe
  function updateTradeEventMarkers() {
    if (!priceChart) return;

    priceChart.options.plugins.annotation.annotations = {};

    const filteredEvents = filterEventsByTimeframe(tradeEvents, currentTimeframe);

    filteredEvents.forEach((event, index) => {
      priceChart.options.plugins.annotation.annotations[`event${index}`] = {
        type: 'point',
        xValue: event.timestamp,
        yValue: event.price,
        backgroundColor: event.type === 'buy' ? '#00C805' : '#FF6B35',
        borderColor: '#ffffff',
        borderWidth: 2,
        radius: 6,
        label: {
          enabled: true,
          content: event.type === 'buy' ? '↑ BUY' : '↓ SELL',
          position: event.type === 'buy' ? 'bottom' : 'top',
          backgroundColor: event.type === 'buy' ? '#00C805' : '#FF6B35',
          color: '#000000',
          font: {
            weight: 'bold',
            size: 11
          },
          padding: 4,
          yAdjust: event.type === 'buy' ? -8 : 8
        }
      };
    });
  }

  // Filter data based on timeframe
  function filterDataByTimeframe(data, timeframe) {
    if (!data.length) return [];

    const now = new Date();
    let cutoff;

    switch (timeframe) {
      case '1d':
        cutoff = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case '1w':
        cutoff = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '1m':
        cutoff = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '3m':
        cutoff = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        cutoff = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    }

    return data.filter(point => new Date(point.x) >= cutoff);
  }

  // Filter events based on timeframe
  function filterEventsByTimeframe(events, timeframe) {
    if (!events.length) return [];

    const now = new Date();
    let cutoff;

    switch (timeframe) {
      case '1d':
        cutoff = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case '1w':
        cutoff = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '1m':
        cutoff = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '3m':
        cutoff = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        cutoff = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    }

    return events.filter(event => new Date(event.timestamp) >= cutoff);
  }

  // Update chart timeframe
  function updateChartTimeframe(timeframe) {
    if (!priceChart) return;

    priceChart.data.datasets[0].data = filterDataByTimeframe(priceHistory, timeframe);
    updateTradeEventMarkers();
    priceChart.update();
  }

  // WebSocket connection
  const ws = new WebSocket('ws://' + location.hostname + ':8182');

  ws.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data);
      botRunning = data.botRunning;

      // Handle historical prices on initial connection
      if (data.historicalPrices && data.historicalPrices.length > 0) {
        console.log(`Received ${data.historicalPrices.length} historical price points`);

        priceHistory = [];

        data.historicalPrices.forEach(point => {
          priceHistory.push({
            x: new Date(point.timestamp),
            y: point.price
          });
        });

        if (priceChart) {
          priceChart.data.datasets[0].data = filterDataByTimeframe(priceHistory, currentTimeframe);
          priceChart.update();
        }

        console.log(`Loaded ${priceHistory.length} price points into chart`);
      }

      // Handle new single price point updates
      if (data.newPricePoint) {
        const newPoint = {
          x: new Date(data.newPricePoint.timestamp),
          y: data.newPricePoint.price
        };

        priceHistory.push(newPoint);

        if (priceHistory.length > 1000) {
          priceHistory = priceHistory.slice(-1000);
        }

        if (priceChart) {
          priceChart.data.datasets[0].data = filterDataByTimeframe(priceHistory, currentTimeframe);
          priceChart.update('none');
        }
      }

      // Fallback: Update chart with current price if available
      else if (data.btcPrice && !data.historicalPrices) {
        const price = parseFloat(data.btcPrice);
        if (price > 0) {
          console.log(`Received BTC price: $${price}`);
          updateChart(price);
        }
      }

      // Check for trade events in logs
      if (data.logs && data.logs.length) {
        const lastLog = data.logs[data.logs.length - 1];

        if (lastLog.includes('BUY order placed:') && data.btcPrice) {
          addTradeEvent('buy', data.btcPrice);
        }

        if (lastLog.includes('SELL order placed:') && data.btcPrice) {
          addTradeEvent('sell', data.btcPrice);
        }
      }

      // Update status
      statusEl.textContent = botRunning ? 'Running' : 'Stopped';
      statusEl.className = `stat-value ${botRunning ? 'status-running' : 'status-stopped'}`;

      // Update basic stats
      tradesEl.textContent = data.trades || 0;

      // Update financial data
      if (data.balance) {
        balanceEl.textContent = `$${parseFloat(data.balance).toFixed(2)}`;
      }

      if (data.btcHoldings) {
        btcHoldingsEl.textContent = parseFloat(data.btcHoldings).toFixed(8);
      }

      if (data.btcPrice) {
        btcPriceEl.textContent = `$${parseFloat(data.btcPrice).toFixed(2)}`;
      }

      if (data.portfolioValue) {
        portfolioValueEl.textContent = `$${parseFloat(data.portfolioValue).toFixed(2)}`;
      }

      if (data.pnl !== undefined) {
        const pnlValue = parseFloat(data.pnl);
        pnlEl.textContent = `${pnlValue >= 0 ? '+' : ''}$${pnlValue.toFixed(2)}`;
        pnlEl.className = `stat-value ${pnlValue >= 0 ? 'positive' : 'negative'}`;
      }

      if (data.stopLoss) {
        stopLossEl.textContent = `$${parseFloat(data.stopLoss).toFixed(2)}`;
      } else {
        stopLossEl.textContent = 'None';
      }

      // Update available balance for slider
      if (data.balance) {
        updateBalanceSlider(data.balance);
      }

      // Update strategy status if available
      if (data.strategyStatus && data.strategyStatus.strategies) {
        updateStrategyStatusDisplay(data.strategyStatus);
      }

      // Update logs
      if (data.logs && data.logs.length) {
        logEl.innerHTML = data.logs.slice(-100).map(l => `<div class="log-entry">${l}</div>`).join('');
        logEl.scrollTop = logEl.scrollHeight;
      }

      toggleBtn.textContent = botRunning ? 'Stop Bot' : 'Start Bot';
    } catch (error) {
      console.error('Error processing WebSocket message:', error);
    }
  };

  ws.onopen = () => {
    console.log('WebSocket connected');
  };

  ws.onclose = () => {
    console.log('WebSocket disconnected');
  };

  ws.onerror = (error) => {
    console.error('WebSocket error:', error);
  };

  // Event listeners - add null checks
  console.log('📝 Setting up DOMContentLoaded listener...');
  document.addEventListener('DOMContentLoaded', function () {
    console.log('🎯 DOM loaded, initializing...');
    console.log('🔧 About to call initializeChart()');
    initializeChart();
    console.log('🔧 About to call initializePage()');
    initializePage();
    console.log('✅ Initialization complete');
  });

  // Initialize DOM elements
  function initializeDOMElements() {
    console.log('🔗 Initializing DOM elements...');
    statusEl = document.getElementById('status');
    tradesEl = document.getElementById('trades');
    balanceEl = document.getElementById('balance');
    btcHoldingsEl = document.getElementById('btcHoldings');
    btcPriceEl = document.getElementById('btcPrice');
    portfolioValueEl = document.getElementById('portfolioValue');
    pnlEl = document.getElementById('pnl');
    stopLossEl = document.getElementById('stopLoss');
    logEl = document.getElementById('log');
    toggleBtn = document.getElementById('toggleBtn');
    strategySelect = document.getElementById('strategySelect');
    applyStrategyBtn = document.getElementById('applyStrategy');
    maxBalanceSlider = document.getElementById('maxBalanceSlider');
    maxBalanceValue = document.getElementById('maxBalanceValue');
    availableBalanceEl = document.getElementById('availableBalance');
    activeStrategiesEl = document.getElementById('activeStrategies');
    lastRebalanceEl = document.getElementById('lastRebalance');
    rebalanceFrequencyEl = document.getElementById('rebalanceFrequency');
    console.log('✅ DOM elements initialized');
  }

  // Initialize page on load
  async function initializePage() {
    console.log('Initializing page...');
    initializeDOMElements();
    await loadPortfolioStrategies();
    await loadSavedSettings();
    console.log('Page initialization complete');
  }

  // Toggle bot
  if (toggleBtn) {
    toggleBtn.addEventListener('click', () => {
      fetch('/api/remote', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: botRunning ? 'stop' : 'start' })
      });
    });
  }

  if (maxBalanceSlider && maxBalanceValue) {
    maxBalanceSlider.addEventListener('input', (e) => {
      maxTradingBalance = parseFloat(e.target.value);
      maxBalanceValue.textContent = maxTradingBalance.toFixed(0);
      updateMaxTradingBalance(maxTradingBalance);
    });

    // Initialize slider value
    maxBalanceValue.textContent = maxBalanceSlider.value;
  }

  // Fetch and update portfolio data
  async function updatePortfolioData() {
    try {
      const response = await fetch('/api/portfolio');
      if (response.ok) {
        const data = await response.json();
        updatePortfolioDisplay(data);

        // Update charts with latest data
        await updateCharts();
      } else {
        console.error('Portfolio API response not ok:', response.status, response.statusText);
      }
    } catch (error) {
      console.error('Error fetching portfolio data:', error);
    }
  }

  // Update portfolio display - updates existing strategy cards instead of replacing them
  function updatePortfolioDisplay(data) {

    // Update overview stats
    const activeStrategiesEl = document.getElementById('activeStrategies');
    const lastRebalanceEl = document.getElementById('lastRebalance');
    const rebalanceFrequencyEl = document.getElementById('rebalanceFrequency');

    if (activeStrategiesEl) activeStrategiesEl.textContent = Object.keys(data.strategies).length;
    if (lastRebalanceEl) lastRebalanceEl.textContent = new Date(data.lastRebalance).toLocaleString();
    if (rebalanceFrequencyEl) rebalanceFrequencyEl.textContent = data.rebalanceFrequency;

    // Update existing strategy cards instead of replacing them
    const strategiesGrid = document.getElementById('strategiesGrid');
    if (!strategiesGrid) {
      console.error('strategiesGrid element not found!');
      return;
    }

    // Update allocation badges and stats in existing strategy control cards
    const effectiveBalance = parseFloat(data.totalBalance || 0);
    const actualBalance = parseFloat(data.actualBalance || 0);
    const isLimited = data.maxTradingBalance !== null && actualBalance > data.maxTradingBalance;

    Object.entries(data.strategies).forEach(([strategyName, stats]) => {
      // Find the strategy card by its data attribute (much more reliable)
      const strategyCard = document.querySelector(`.strategy-control-card[data-strategy-id="${strategyName}"]`);

      if (strategyCard) {
        // Update underfunded styling
        const isUnderfunded = stats.status?.isUnderfunded || false;
        if (isUnderfunded) {
          strategyCard.classList.add('underfunded');
        } else {
          strategyCard.classList.remove('underfunded');
        }
        // Update allocation badge
        const allocationBadge = strategyCard.querySelector('.allocation-badge');
        if (allocationBadge) {
          allocationBadge.textContent = stats.allocation;
        }

        // Calculate and update bucket size
        const rawAllocation = data.allocations ? data.allocations[strategyName] : 0;
        const bucketSize = effectiveBalance * rawAllocation;

        // Update stats section (bucket size and performance stats)
        const statsSection = strategyCard.querySelector('.strategy-stats');
        if (statsSection) {
          // Find stats by their labels instead of position (more reliable)
          const statRows = statsSection.querySelectorAll('.stat-row');

          statRows.forEach(row => {
            const label = row.querySelector('span:first-child')?.textContent;
            const valueEl = row.querySelector('span:last-child');

            if (label && valueEl) {
              switch (label) {
                case 'Bucket Size:':
                  valueEl.textContent = `$${bucketSize.toFixed(2)}${isLimited ? ' (limited)' : ''}`;
                  break;
                case 'Total Return:':
                  valueEl.textContent = stats.totalReturn;
                  valueEl.className = parseFloat(stats.totalReturn) >= 0 ? 'positive' : 'negative';
                  break;
                case 'Win Rate:':
                  valueEl.textContent = stats.winRate;
                  break;
                case 'Trades:':
                  valueEl.textContent = stats.trades;
                  break;
                case 'Sharpe Ratio:':
                  valueEl.textContent = stats.sharpeRatio;
                  break;
              }
            }
          });
        }
      } else {
        console.warn(`Could not find card for strategy: ${strategyName}`);
      }
    });
  }

  // Strategy Configuration Modal Functions
  let currentConfigStrategy = null;

  async function openStrategyConfig(strategyId) {
    try {
      currentConfigStrategy = strategyId;

      // Get strategy details
      const strategy = availableStrategies[strategyId];
      if (!strategy) {
        console.error('Strategy not found:', strategyId);
        return;
      }

      // Get current configuration from portfolio
      const portfolioResponse = await fetch('/api/portfolio/status');
      const portfolioData = await portfolioResponse.json();
      const currentConfig = portfolioData.strategies[strategyId]?.config || {};

      // Update modal content
      document.getElementById('configModalTitle').textContent = `Configure ${strategy.displayName}`;
      document.getElementById('configModalDescription').textContent = strategy.description;

      // Generate config fields
      generateConfigFields(strategy.config, currentConfig);

      // Show modal
      document.getElementById('strategyConfigModal').classList.add('show');

    } catch (error) {
      console.error('Error opening strategy config:', error);
      alert('Failed to load strategy configuration');
    }
  }

  function closeStrategyConfig() {
    document.getElementById('strategyConfigModal').classList.remove('show');
    currentConfigStrategy = null;
  }

  function generateConfigFields(schema, currentConfig) {
    const fieldsContainer = document.getElementById('configFields');
    fieldsContainer.innerHTML = '';

    Object.entries(schema).forEach(([key, fieldConfig]) => {
      const fieldDiv = document.createElement('div');
      fieldDiv.className = 'config-field';

      const currentValue = currentConfig[key] !== undefined ? currentConfig[key] : fieldConfig.default;

      let inputHtml = '';

      switch (fieldConfig.type) {
        case 'number':
          inputHtml = `
          <input
            type="number"
            id="config_${key}"
            name="${key}"
            value="${currentValue || ''}"
            min="${fieldConfig.min || ''}"
            max="${fieldConfig.max || ''}"
            step="${fieldConfig.step || 'any'}"
            required
          >
        `;
          break;

        case 'boolean':
          inputHtml = `
          <div class="checkbox-wrapper">
            <input
              type="checkbox"
              id="config_${key}"
              name="${key}"
              ${currentValue ? 'checked' : ''}
            >
            <label for="config_${key}">${fieldConfig.label}</label>
          </div>
        `;
          break;

        case 'select':
          const options = fieldConfig.options || [];
          inputHtml = `
          <select id="config_${key}" name="${key}" required>
            ${options.map(option =>
            `<option value="${option.value}" ${currentValue === option.value ? 'selected' : ''}>${option.label}</option>`
          ).join('')}
          </select>
        `;
          break;

        default:
          inputHtml = `
          <input
            type="text"
            id="config_${key}"
            name="${key}"
            value="${currentValue || ''}"
            required
          >
        `;
      }

      fieldDiv.innerHTML = `
      ${fieldConfig.type !== 'boolean' ? `<label for="config_${key}">${fieldConfig.label}</label>` : ''}
      ${inputHtml}
      ${fieldConfig.description ? `<div class="field-description">${fieldConfig.description}</div>` : ''}
    `;

      fieldsContainer.appendChild(fieldDiv);
    });
  }

  async function resetStrategyConfig() {
    if (!currentConfigStrategy) return;

    const strategy = availableStrategies[currentConfigStrategy];
    if (!strategy) return;

    // Reset all fields to default values
    Object.entries(strategy.config).forEach(([key, fieldConfig]) => {
      const field = document.getElementById(`config_${key}`);
      if (field) {
        if (fieldConfig.type === 'boolean') {
          field.checked = fieldConfig.default || false;
        } else {
          field.value = fieldConfig.default || '';
        }
      }
    });
  }

  // Handle form submission
  document.addEventListener('DOMContentLoaded', () => {
    const form = document.getElementById('strategyConfigForm');
    if (form) {
      form.addEventListener('submit', async (e) => {
        e.preventDefault();
        await saveStrategyConfig();
      });
    }
  });

  async function saveStrategyConfig() {
    if (!currentConfigStrategy) return;

    try {
      const formData = new FormData(document.getElementById('strategyConfigForm'));
      const config = {};

      // Get strategy schema for type conversion
      const strategy = availableStrategies[currentConfigStrategy];
      const schema = strategy.config;

      // Convert form data to proper types
      for (const [key, value] of formData.entries()) {
        const fieldConfig = schema[key];
        if (fieldConfig) {
          switch (fieldConfig.type) {
            case 'number':
              config[key] = parseFloat(value);
              break;
            case 'boolean':
              config[key] = true; // Checkbox is only in formData if checked
              break;
            default:
              config[key] = value;
          }
        }
      }

      // Handle unchecked checkboxes
      Object.entries(schema).forEach(([key, fieldConfig]) => {
        if (fieldConfig.type === 'boolean' && config[key] === undefined) {
          config[key] = false;
        }
      });

      // Send update request
      const response = await fetch('/api/portfolio/strategy/config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          strategyName: currentConfigStrategy,
          config
        })
      });

      if (response.ok) {
        closeStrategyConfig();
        // Refresh portfolio data to show updated config
        await updatePortfolioData();
        alert('Strategy configuration updated successfully!');
      } else {
        const error = await response.json();
        alert(`Failed to update configuration: ${error.error}`);
      }

    } catch (error) {
      console.error('Error saving strategy config:', error);
      alert('Failed to save configuration');
    }
  }

  // Close modal when clicking outside
  document.addEventListener('click', (e) => {
    const modal = document.getElementById('strategyConfigModal');
    if (e.target === modal) {
      closeStrategyConfig();
    }
  });

  // Close modal with Escape key
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
      closeStrategyConfig();
    }
  });

  // Call updatePortfolioData on page load
  document.addEventListener('DOMContentLoaded', () => {
    updatePortfolioData();
  });

  // Set interval to update portfolio data every 30 seconds
  setInterval(updatePortfolioData, 30000);

  // Update status function - add portfolio update
  async function updateStatus() {
    try {
      const response = await fetch('/api/status');
      const data = await response.json();

      // ... existing status updates ...

      // Update portfolio data
      await updatePortfolioData();

    } catch (error) {
      console.error('Error updating status:', error);
    }
  }

  // Load and display all available strategies with portfolio data
  async function loadPortfolioStrategies() {
    try {
      // Load available strategies
      const strategiesResponse = await fetch('/api/strategies');
      const strategiesData = await strategiesResponse.json();

      // Load current portfolio data with status information
      const portfolioResponse = await fetch('/api/portfolio/status');
      const portfolioData = portfolioResponse.ok ? await portfolioResponse.json() : { strategies: {}, allocations: {} };

      displayStrategiesGrid(strategiesData.strategies, portfolioData);

    } catch (error) {
      console.error('Error loading portfolio strategies:', error);
    }
  }

  // Display strategies grid with controls
  function displayStrategiesGrid(availableStrategies, portfolioData) {
    const strategiesGrid = document.getElementById('strategiesGrid');
    strategiesGrid.innerHTML = '';

    let activeCount = 0;
    const effectiveBalance = parseFloat(portfolioData.totalBalance || 0);
    const actualBalance = parseFloat(portfolioData.actualBalance || 0);
    const isLimited = portfolioData.maxTradingBalance !== null && actualBalance > portfolioData.maxTradingBalance;

    availableStrategies.forEach(strategy => {
      const isActive = portfolioData.strategies && portfolioData.strategies[strategy.id];
      const stats = isActive ? portfolioData.strategies[strategy.id] : null;
      const allocation = portfolioData.allocations ? portfolioData.allocations[strategy.id] : 0;

      if (isActive) activeCount++;

      const bucketSize = effectiveBalance * allocation;
      const isUnderfunded = stats?.status?.isUnderfunded || false;

      const strategyCard = document.createElement('div');
      let cardClasses = `strategy-control-card ${isActive ? 'active' : 'inactive'}`;
      if (isUnderfunded) {
        cardClasses += ' underfunded';
      }
      strategyCard.className = cardClasses;
      strategyCard.setAttribute('data-strategy-id', strategy.id);
      strategyCard.innerHTML = `
      <div class="strategy-header">
        <div class="strategy-title">
          <h4>${strategy.displayName}</h4>
          <div class="strategy-controls">
            <button class="config-btn" onclick="openStrategyConfig('${strategy.id}')" title="Configure Strategy">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="3"></circle>
                <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"></path>
              </svg>
            </button>
            <div class="strategy-toggle">
              <label class="toggle-switch">
                <input type="checkbox" ${isActive ? 'checked' : ''}
                     onchange="toggleStrategy('${strategy.id}', this.checked)">
                <span class="toggle-slider"></span>
              </label>
            </div>
          </div>
        </div>
        ${isActive ? `<div class="allocation-badge">${(allocation * 100).toFixed(1)}%</div>` : ''}
      </div>
      
      <div class="strategy-description">
        <p>${strategy.description}</p>
      </div>
      
      ${isActive ? `
        <div class="strategy-status">
          <div class="status-section">
            <h5>Current Status</h5>
            <div class="status-item">
              <span class="status-label">Last Decision:</span>
              <span class="status-value ${stats.status?.lastDecision ? 'positive' : 'neutral'}">${stats.status?.lastDecision ? 'ENTER' : 'WAIT'}</span>
            </div>
            <div class="status-item">
              <span class="status-label">Reason:</span>
              <span class="status-reason">${stats.status?.lastDecisionReason || 'No recent decisions'}</span>
            </div>
            <div class="status-item">
              <span class="status-label">Next Signal:</span>
              <span class="status-next">${stats.status?.nextSignal || 'Monitoring market'}</span>
            </div>
            ${stats.status?.lastTradeTime ? `
              <div class="status-item">
                <span class="status-label">Last Trade:</span>
                <span class="status-trade">${stats.status.lastTradeType?.toUpperCase()} at $${stats.status.lastTradePrice?.toFixed(2)} (${new Date(stats.status.lastTradeTime).toLocaleString()})</span>
              </div>
            ` : ''}
          </div>
        </div>

        ${isUnderfunded ? `
          <div class="underfunded-warning">
            <h5>Insufficient Funding</h5>
            <p>This strategy needs $${bucketSize.toFixed(2)} but requires minimum $5.00 to operate.</p>
            <p class="suggestion">Increase max trading balance to $${Math.ceil((5 / allocation) / 10) * 10} or reduce number of active strategies.</p>
          </div>
        ` : ''}

        <div class="strategy-stats">
          <div class="stat-row">
            <span>Bucket Size:</span>
            <span>$${bucketSize.toFixed(2)}${isLimited ? ' (limited)' : ''}</span>
          </div>
          <div class="stat-row">
            <span>Total Return:</span>
            <span class="${parseFloat(stats.totalReturn) >= 0 ? 'positive' : 'negative'}">${stats.totalReturn}</span>
          </div>
          <div class="stat-row">
            <span>Win Rate:</span>
            <span>${stats.winRate}</span>
          </div>
          <div class="stat-row">
            <span>Trades:</span>
            <span>${stats.trades}</span>
          </div>
          <div class="stat-row">
            <span>Sharpe Ratio:</span>
            <span>${stats.sharpeRatio}</span>
          </div>
        </div>
      ` : `
        <div class="strategy-inactive">
          <p>Strategy is currently disabled</p>
        </div>
      `}
    `;
      strategiesGrid.appendChild(strategyCard);
    });

    // Update active strategies count
    document.getElementById('activeStrategies').textContent = activeCount;

    // Update other portfolio stats if available
    if (portfolioData.lastRebalance) {
      document.getElementById('lastRebalance').textContent = new Date(portfolioData.lastRebalance).toLocaleString();
    }
    if (portfolioData.rebalanceFrequency) {
      document.getElementById('rebalanceFrequency').textContent = portfolioData.rebalanceFrequency;
    }
  }

  // Update strategy status display in real-time
  function updateStrategyStatusDisplay(strategyData) {
    if (!strategyData.strategies) return;

    Object.entries(strategyData.strategies).forEach(([strategyName, stats]) => {
      // Find the strategy card by its data attribute (more reliable)
      const strategyCard = document.querySelector(`.strategy-control-card[data-strategy-id="${strategyName}"]`);

      if (strategyCard && stats.status) {
        // Update underfunded styling
        const isUnderfunded = stats.status.isUnderfunded || false;
        if (isUnderfunded) {
          strategyCard.classList.add('underfunded');
        } else {
          strategyCard.classList.remove('underfunded');
        }
        // Update status section if it exists
        const statusSection = strategyCard.querySelector('.strategy-status');
        if (statusSection) {
          const lastDecisionValue = statusSection.querySelector('.status-value');
          const reasonElement = statusSection.querySelector('.status-reason');
          const nextSignalElement = statusSection.querySelector('.status-next');

          if (lastDecisionValue) {
            lastDecisionValue.textContent = stats.status.lastDecision ? 'ENTER' : 'WAIT';
            lastDecisionValue.className = `status-value ${stats.status.lastDecision ? 'positive' : 'neutral'}`;
          }

          if (reasonElement) {
            reasonElement.textContent = stats.status.lastDecisionReason || 'No recent decisions';
          }

          if (nextSignalElement) {
            nextSignalElement.textContent = stats.status.nextSignal || 'Monitoring market';
          }
        }
      }
    });
  }

  // Toggle strategy on/off with safety checks
  async function toggleStrategy(strategyId, enabled) {
    try {
      const response = await fetch('/api/portfolio/strategy/toggle', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ strategyName: strategyId, enabled })
      });

      if (response.ok) {
        // Reload the strategies display
        await loadPortfolioStrategies();
        console.log(`${enabled ? 'Enabled' : 'Disabled'} strategy: ${strategyId}`);
      } else {
        const errorData = await response.json();

        // Handle safety check failure for disable
        if (!enabled && errorData.canForceDisable) {
          const confirmed = await showPositionWarningDialog(strategyId, errorData);
          if (confirmed) {
            await forceDisableStrategy(strategyId, true); // Close position
          } else {
            // Reset the toggle since user cancelled
            await loadPortfolioStrategies();
          }
          return;
        }

        console.error('Failed to toggle strategy:', errorData.error);
        alert(`Failed to ${enabled ? 'enable' : 'disable'} strategy: ${errorData.error}`);
        await loadPortfolioStrategies(); // Reset toggle state
      }
    } catch (error) {
      console.error('Error toggling strategy:', error);
      alert('Error toggling strategy. Please try again.');
      // Reload to reset the toggle state
      await loadPortfolioStrategies();
    }
  }

  // Show warning dialog for strategies with open positions
  async function showPositionWarningDialog(strategyId, errorData) {
    const warnings = errorData.warnings || [];
    const position = errorData.position || {};

    const message = `⚠️ WARNING: Cannot safely disable "${strategyId}"

${warnings.join('\n')}

Options:
• Cancel - Keep strategy enabled
• OK - Force disable and close position at market price

This will sell ${position.quantity?.toFixed(8) || '0'} BTC immediately.

Do you want to force disable and close the position?`;

    return confirm(message);
  }

  // Force disable strategy with position closure
  async function forceDisableStrategy(strategyId, closePosition = false) {
    try {
      const response = await fetch('/api/portfolio/strategy/force-disable', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ strategyName: strategyId, closePosition })
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Failed to force disable strategy:', errorData.error);
        alert(`Failed to force disable strategy: ${errorData.error}`);
        return;
      }

      const result = await response.json();
      console.log(`Strategy ${strategyId} force disabled:`, result);

      if (result.actionsExecuted && result.actionsExecuted.length > 0) {
        const actions = result.actionsExecuted.map(a => a.reason).join('\n');
        alert(`Strategy disabled successfully!\n\nActions executed:\n${actions}`);
      }

      // Refresh the portfolio data
      await loadPortfolioStrategies();

    } catch (error) {
      console.error('Error force disabling strategy:', error);
      alert('Error force disabling strategy. Please try again.');
      await loadPortfolioStrategies();
    }
  }
}






