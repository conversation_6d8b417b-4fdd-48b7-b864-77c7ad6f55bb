{"name": "big.js", "description": "A small, fast, easy-to-use library for arbitrary-precision decimal arithmetic", "version": "6.2.2", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "exports": {".": {"import": "./big.mjs", "require": "./big.js"}, "./big.mjs": "./big.mjs", "./big.js": "./big.js", "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/MikeMcl/big.js.git"}, "main": "big", "browser": "big.js", "module": "big.mjs", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/MikeMcl/big.js/issues"}, "engines": {"node": "*"}, "license": "MIT", "scripts": {"test": "node ./test/runner.js"}, "files": ["big.js", "big.mjs"], "funding": {"type": "opencollective", "url": "https://opencollective.com/bigjs"}}